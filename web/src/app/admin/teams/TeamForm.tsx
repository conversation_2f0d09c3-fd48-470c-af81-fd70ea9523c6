"use client";

import { Form, Formik } from "formik";
import { PopupSpec } from "@/components/admin/connectors/Popup";
import {
  TextFormField,
} from "@/components/admin/connectors/Field";
import { createTeam, updateTeam } from "./lib";
import { Modal } from "@/components/Modal";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import Text from "@/components/ui/text";
import { Team, TeamUser } from "./types";
import { SearchMultiSelectDropdown } from "@/components/Dropdown"; // Re-added SearchMultiSelectDropdown
import useSWR, { mutate } from "swr";
import { useState } from "react";
import { FiTrash2 } from "react-icons/fi"; // Only FiTrash2 needed
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { UserRole, USER_ROLE_LABELS } from "@/lib/types";
import { useUser } from "@/components/user/UserProvider";

// Type for users with status information
interface UserWithStatus {
  id: string;
  email: string;
  status: string;
  role: string;
  is_active: boolean;
}

interface TeamFormProps {
  onClose: () => void;
  setPopup: (popupSpec: PopupSpec | null) => void;
  onCreateTeam: (team: Team) => void;
  team?: Team;
}

export const TeamForm = ({
  onClose,
  setPopup,
  onCreateTeam,
  team,
}: TeamFormProps) => {
  const isUpdate = team !== undefined;
  const [selectedUsers, setSelectedUsers] = useState<TeamUser[]>(
    team?.users || []
  );
  const { user: currentUser } = useUser();

  // Get available roles for the dropdown
  const getAvailableRoles = () => {
    // Both Admin and Team Admin can only assign Team Admin and Basic roles within a team
    return Object.entries(USER_ROLE_LABELS).filter(([role]) =>
      [UserRole.TEAM_ADMIN, UserRole.BASIC].includes(role as UserRole)
    );
  };

  // Fetch all accepted users for selection
  const { data: usersResponse, error: usersError } = useSWR<{items: UserWithStatus[], total_items: number}>(
    "/api/manage/users/accepted?page_size=1000",
    (url: string) => fetch(url).then((r) => r.json())
  );

  // Extract users from the paginated response
  const allUsers: TeamUser[] = usersResponse?.items
    ?.filter(user => user.role !== UserRole.ADMIN) // Filter out ADMIN users from the selection list
    .map((user: UserWithStatus) => ({
      id: user.id,
      email: user.email,
      role: user.role as UserRole,
    })) || [];

  // Filter out users already selected
  const availableUsers = allUsers.filter(
    (user) => !selectedUsers.some((selected) => selected.id === user.id)
  );

  return (
    <Modal onOutsideClick={onClose} width="w-11/12 md:w-2/6 max-w-lg">
      <>
        <h2 className="text-xl font-bold flex">
          {isUpdate ? "Update Team" : "Create a new Team"}
        </h2>

        <Separator />

        <Formik
          initialValues={{
            name: team?.name || "",
          }}
          onSubmit={async (values, formikHelpers) => {
            formikHelpers.setSubmitting(true);

            // Validation: Check if team name is empty
            if (!values.name.trim()) {
              setPopup({
                message: "Team name cannot be empty. Please enter a team name.",
                type: "error",
              });
              formikHelpers.setSubmitting(false);
              return;
            }

            // Validation: Check if no users are selected
            if (selectedUsers.length === 0) {
              setPopup({
                message: "Please add at least one user to the team.",
                type: "error",
              });
              formikHelpers.setSubmitting(false);
              return;
            }

            const payload = {
              ...values,
              members: selectedUsers.map(user => ({ user_id: user.id, role: user.role }))
            };

            let response;
            if (isUpdate) {
              response = await updateTeam(team.id, payload);
            } else {
              response = await createTeam(payload);
            }

            formikHelpers.setSubmitting(false);

            if (response.ok) {
              setPopup({
                message: isUpdate
                  ? "Successfully updated team!"
                  : "Successfully created team!",
                type: "success",
              });

              // Refresh the teams data to keep it up-to-date
              mutate("/api/manage/admin/user-teams");

              if (!isUpdate) {
                onCreateTeam(await response.json());
              }

              onClose();
            } else {
              let errorMsg = `Error ${isUpdate ? "updating" : "creating"} team`;
              try {
                const errorData = await response.json();
                errorMsg = errorData.detail || errorData.message || errorMsg;
              } catch (e) {
                // If response is not JSON, use generic error message
                errorMsg = `Error ${isUpdate ? "updating" : "creating"} team: ${response.statusText}`;
              }
              setPopup({
                message: errorMsg,
                type: "error",
              });
            }
          }}
        >
          {({ values, setFieldValue, isSubmitting }) => (
            <Form className="w-full overflow-visible">
              <Text className="mb-4 text-lg">
                Enter a name for your team and select users to add.
              </Text>

              <TextFormField
                name="name"
                label="Team Name:"
                autoCompleteDisabled={true}
              />

              <div className="mb-4">
                <Text className="mb-2 font-medium">Select Users:</Text>

                {/* User selection dropdown */}
                {availableUsers.length > 0 && (
                  <SearchMultiSelectDropdown
                    options={availableUsers.map((user) => ({
                      name: user.email,
                      value: user.id,
                    }))}
                    onSelect={(option) => {
                      const user = allUsers.find((u) => u.id === option.value);
                      if (user) {
                        setSelectedUsers([
                          ...selectedUsers,
                          { ...user, role: UserRole.BASIC }, // Default role to BASIC
                        ]);
                      }
                    }}
                    itemComponent={({ option }) => (
                      <div className="flex items-center px-4 py-2.5 cursor-pointer hover:bg-background-100">
                        <span className="flex-grow">{option.name}</span>
                      </div>
                    )}
                  />
                )}

                {/* Display selected users with roles */}
                {selectedUsers.length > 0 && (
                  <div className="mt-4 space-y-3">
                    <Text className="text-sm text-gray-600 dark:text-gray-300 mb-2">Selected users:</Text>
                    {selectedUsers.map((user, index) => (
                      <div
                        key={user.id}
                        className="flex items-center gap-2 bg-accent text-accent-foreground px-2 py-1 rounded-md text-sm flex-wrap"
                      >
                        <span>{user.email}</span>
                        <Select
                          value={user.role}
                          onValueChange={(value) => {
                            const newSelectedUsers = [...selectedUsers];
                            newSelectedUsers[index] = { ...newSelectedUsers[index], role: value as UserRole };
                            setSelectedUsers(newSelectedUsers);
                          }}
                        >
                          <SelectTrigger className="w-[120px]">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {getAvailableRoles().map(([role, label]) => (
                              <SelectItem key={role} value={role}>
                                {label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedUsers(selectedUsers.filter((u) => u.id !== user.id));
                          }}
                          className="px-2"
                        >
                          <FiTrash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}

                {!availableUsers.length && !usersError && (
                  <Text className="text-gray-500 text-sm mt-2">
                    No users are available to add to teams.
                  </Text>
                )}

                {usersError && (
                  <Text className="text-red-500 text-sm mt-2">
                    Error loading users. Please try again.
                  </Text>
                )}
              </div>

              <Button
                type="submit"
                size="sm"
                variant="submit"
                disabled={isSubmitting}
              >
                {isUpdate ? "Update!" : "Create!"}
              </Button>
            </Form>
          )}
        </Formik>
      </>
    </Modal>
  );
};
