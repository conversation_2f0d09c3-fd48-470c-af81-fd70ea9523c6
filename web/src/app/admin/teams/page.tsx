"use client";

import { ThreeDotsLoader } from "@/components/Loading";
import { AdminPageTitle } from "@/components/admin/Title";
import { FiUsers, FiEdit2 } from "react-icons/fi";
import { ErrorCallout } from "@/components/ErrorCallout";
import useSWR, { mutate } from "swr";
import { deleteTeam } from "./lib";
import { Team } from "./types";
import { usePopup } from "@/components/admin/connectors/Popup";
import { DeleteButton } from "@/components/DeleteButton";
import { Separator } from "@/components/ui/separator";
import Text from "@/components/ui/text";
import CreateButton from "@/components/ui/createButton";
import { TeamForm } from "./TeamForm";
import { useState, useEffect } from "react";
import { ConfirmEntityModal } from "@/components/modals/ConfirmEntityModal";
import { RemoveUserFromTeamButton } from "@/components/admin/users/buttons/RemoveUserFromTeamButton";
import { PopupSpec } from "@/components/admin/connectors/Popup";
import { InvitedUserSnapshot } from "@/lib/types";
import { InviteUserButton } from "@/components/admin/users/buttons/InviteUserButton";
import { MoreHorizontal } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { setStoredTeamId } from "@/lib/team";

export default function Page() {
  return (
    <div className="mx-auto container">
      <AdminPageTitle title="Teams" icon={<FiUsers size={32} />} />
      <Main />
    </div>
  );
}

function Main() {
  const { popup, setPopup } = usePopup();
  const [showCreateUpdateForm, setShowCreateUpdateForm] = useState(false);
  const [selectedTeam, setSelectedTeam] = useState<Team | undefined>();
  const [teamToDelete, setTeamToDelete] = useState<Team | null>(null);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [expandedRowsState, setExpandedRowsState] = useState<{ [key: number]: boolean }>({});

  const { data: teams, isLoading, error, mutate } = useSWR<Team[]>(
    "/api/manage/admin/user-teams",
    { fetcher: (url) => fetch(url).then((r) => r.json()) }
  );

  const handleTeamSelection = async (teamId: number) => {
    setStoredTeamId(teamId);
    // Trigger a dummy fetch to ensure the backend updates the Current-Team-ID cookie
    await fetch("/api/health", { headers: { "X-Current-Team-ID": String(teamId) } });
  };

  useEffect(() => {
    if (teams && teams.length > 0 && !selectedTeam) {
      // Set the first team as selected by default if no team is selected
      // and update the Current-Team-ID in local storage and cookie
      handleTeamSelection(teams[0].id);
    } else if (selectedTeam) {
      // If a team is already selected (e.g., after an edit), ensure the cookie is updated
      handleTeamSelection(selectedTeam.id);
    }
  }, [teams, selectedTeam]);

  const handleEdit = (team: Team) => {
    setSelectedTeam(team);
    setShowCreateUpdateForm(true);
  };

  const openDeleteModal = (team: Team) => {
    setTeamToDelete(team);
    setDeleteModalOpen(true);
  };

  const closeDeleteModal = () => {
    setDeleteModalOpen(false);
    setTeamToDelete(null);
  };

  const handleDeleteTeam = async () => {
    if (teamToDelete) {
      const response = await deleteTeam(teamToDelete.id);
      if (!response.ok) {
        const errorMsg = await response.text();
        setPopup({
          type: "error",
          message: `Failed to delete team: ${errorMsg}`,
        });
        return;
      }
      mutate();
      closeDeleteModal();
    }
  };

  if (isLoading) return <ThreeDotsLoader />;

  if (error) {
    return (
      <ErrorCallout
        errorTitle="Failed to fetch Teams"
        errorMsg={error?.info?.detail || error.toString()}
      />
    );
  }

  const newTeamButton = (
    <CreateButton
      onClick={() => setShowCreateUpdateForm(true)}
      text="Create Team"
    />
  );

  // Filter out outdated teams and the "Global Admins" team (teamid: -1)
  const upToDateTeams = teams?.filter((team) => team.is_up_to_date && team.id !== -1);

  if (!upToDateTeams || upToDateTeams.length === 0) {
    return (
      <div>
        {popup}
        <Text>No teams found.</Text>
        {newTeamButton}

        {showCreateUpdateForm && (
          <TeamForm
            onCreateTeam={() => mutate()}
            onClose={() => {
              setShowCreateUpdateForm(false);
              setSelectedTeam(undefined);
            }}
            setPopup={setPopup}
            team={selectedTeam}
          />
        )}
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {popup}
      {teamToDelete && deleteModalOpen && (
        <ConfirmEntityModal
          entityType="Team"
          entityName={teamToDelete.name}
          onClose={closeDeleteModal}
          onSubmit={handleDeleteTeam}
        />
      )}
      {newTeamButton}

      <Separator />

      <table className="w-full text-left border-collapse">
        <thead>
          <tr className="border-b">
            <th className="py-2 px-4">Teams</th>
            <th className="py-2 px-4">Users</th>
            <th className="py-2 px-4">Role</th>
            <th className="py-2 px-4">Actions</th>
          </tr>
        </thead>
        <tbody>
          {upToDateTeams.map((team) => (
            <tr key={team.id} className="border-b">
              <td className="py-2 px-4">
                <div
                  className="
                    my-auto flex mb-1 w-fit
                    hover:bg-accent-background-hovered cursor-pointer
                  p-2 rounded-lg border-border text-sm
                "
                  onClick={() => {
                    handleEdit(team);
                    handleTeamSelection(team.id);
                  }}
                >
                  <FiEdit2 className="my-auto mr-2" />
                  {team.name}
                </div>
              </td>
              <td className="py-2 px-4">
                <UsersCell
                  users={team.users}
                  teamId={team.id}
                  setPopup={setPopup}
                  mutate={mutate}
                  expandedRowsState={expandedRowsState}
                  setExpandedRowsState={setExpandedRowsState}
                />
              </td>
              <td className="py-2 px-4">
                <RolesCell
                  users={team.users}
                  teamId={team.id}
                  expandedRowsState={expandedRowsState}
                  setExpandedRowsState={setExpandedRowsState}
                />
              </td>
              <td className="py-2 px-4">
                <DeleteButton
                  onClick={() => openDeleteModal(team)}
                />
              </td>
            </tr>
          ))}
        </tbody>
      </table>

      {showCreateUpdateForm && (
        <TeamForm
          onCreateTeam={() => mutate()}
          onClose={() => {
            setShowCreateUpdateForm(false);
            setSelectedTeam(undefined);
          }}
          setPopup={setPopup}
          team={selectedTeam}
        />
      )}
    </div>
  );
}

// Small helper: inline toggle for `+N more`
import { TeamUser } from "./types"; // Import TeamUser type
import { USER_ROLE_LABELS } from "@/lib/types";

interface UsersCellProps {
  users: TeamUser[];
  teamId: number;
  setPopup: (spec: PopupSpec) => void;
  mutate: () => void;
  expandedRowsState: { [key: number]: boolean };
  setExpandedRowsState: React.Dispatch<React.SetStateAction<{ [key: number]: boolean }>>;
}

function UsersCell({ users, teamId, setPopup, mutate, expandedRowsState, setExpandedRowsState }: UsersCellProps) {
  const showAll = expandedRowsState[teamId] || false;

  const visibleUsers = showAll ? users : users.slice(0, 2);
  const hiddenCount = users.length - 2;

  const toggleShowAll = () => {
    const newState = !showAll;
    setExpandedRowsState(prevState => ({
      ...prevState,
      [teamId]: newState
    }));
  };

  const handleSuccess = () => {
    setPopup({ type: "success", message: "User successfully removed from team!" });
    mutate(); // Re-fetch teams data
  };

  const handleError = (errorMsg: string) => {
    setPopup({ type: "error", message: `Failed to remove user: ${errorMsg}` });
  };

  return (
    <div>
      {visibleUsers.map((u) => (
        <div key={u.id} className="flex my-0.5 justify-between items-center">
          <span>{u.email}</span>
          {/* Removed UserActionsDropdown (which contained the "..." dots) as per user request */}
        </div>
      ))}
      {!showAll && hiddenCount > 0 && (
        <button
          onClick={toggleShowAll}
          className="text-blue-600 hover:underline cursor-pointer text-sm"
        >
          + {hiddenCount} more
        </button>
      )}
    </div>
  );
}



interface RolesCellProps {
  users: TeamUser[];
  teamId: number;
  expandedRowsState: { [key: number]: boolean };
  setExpandedRowsState: React.Dispatch<React.SetStateAction<{ [key: number]: boolean }>>;
}

function RolesCell({ users, teamId, expandedRowsState, setExpandedRowsState }: RolesCellProps) {
  const showAll = expandedRowsState[teamId] || false;

  const visibleRoles = showAll ? users : users.slice(0, 2);
  const hiddenCount = users.length - 2;

  const toggleShowAll = () => {
    const newState = !showAll;
    setExpandedRowsState(prevState => ({
      ...prevState,
      [teamId]: newState
    }));
  };

  return (
    <div>
      {visibleRoles.map((u) => (
        <div key={u.id} className="flex my-0.5">
          {USER_ROLE_LABELS[u.role]}
        </div>
      ))}
      {!showAll && hiddenCount > 0 && (
        <button
          onClick={toggleShowAll}
          className="text-blue-600 hover:underline cursor-pointer text-sm"
        >
          + {hiddenCount} more
        </button>
      )}
    </div>
  );
}
