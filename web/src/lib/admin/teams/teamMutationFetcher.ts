import { TeamArgs } from "@/app/admin/teams/types";

export async function removeUserFromTeam(
  userId: string
): Promise<Response> {
  return fetch(`/api/manage/admin/user-teams/users/${userId}`, {
    method: "DELETE",
  });
}

export async function createTeam(teamArgs: TeamArgs): Promise<Response> {
  return fetch("/api/manage/admin/user-teams", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(teamArgs),
  });
}

export async function updateTeam(
  teamId: number,
  teamArgs: TeamArgs
): Promise<Response> {
  return fetch(`/api/manage/admin/user-teams/${teamId}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(teamArgs),
  });
}
