export const TEAM_STORAGE_KEY = "X-Current-Team-ID";

export function getStoredTeamId(): number | null {
  try {
    const val = window.localStorage.getItem(TEAM_STORAGE_KEY);
    if (!val) return null;
    const parsed = parseInt(val, 10);
    if (Number.isNaN(parsed)) return null;
    // Allow -1 for global admin context
    if (parsed === 0 || parsed < -1) return null;
    return parsed;
  } catch {
    return null;
  }
}

export function clearStoredTeamId(): void {
  try {
    window.localStorage.removeItem(TEAM_STORAGE_KEY);
  } catch {
    // ignore
  }
}

export function setStoredTeamId(teamId: number): void {
  try {
    window.localStorage.setItem(TEAM_STORAGE_KEY, String(teamId));
  } catch {
    // ignore
  }
}
