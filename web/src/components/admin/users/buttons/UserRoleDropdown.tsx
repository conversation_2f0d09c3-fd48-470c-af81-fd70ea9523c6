import {
  type User,
  UserRole,
  USER_ROLE_LABELS,
  INVALID_ROLE_HOVER_TEXT,
} from "@/lib/types";
import userMutationFetcher from "@/lib/admin/users/userMutationFetcher";
import useSWRMutation from "swr/mutation";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { GenericConfirmModal } from "@/components/modals/GenericConfirmModal";
import { useState } from "react";
import { usePaidEnterpriseFeaturesEnabled } from "@/components/settings/usePaidEnterpriseFeaturesEnabled";
import { useUser } from "@/components/user/UserProvider";

const UserRoleDropdown = ({
  user,
  onSuccess,
  onError,
}: {
  user: User;
  onSuccess: () => void;
  onError: (message: string) => void;
}) => {
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [pendingRole, setPendingRole] = useState<string | null>(null);

  const { trigger: setUserRole, isMutating: isSettingRole } = useSWRMutation(
    "/api/manage/set-user-role",
    userMutationFetcher,
    { onSuccess, onError }
  );
  const isPaidEnterpriseFeaturesEnabled = usePaidEnterpriseFeaturesEnabled();
  const { user: currentUser } = useUser(); // Get the current user making the change

  const handleChange = (value: string) => {
    if (value === user.role) return;
    if (user.role === UserRole.CURATOR) {
      setShowConfirmModal(true);
      setPendingRole(value);
    } else {
      setUserRole({
        user_email: user.email,
        new_role: value,
      });
    }
  };

  const handleConfirm = () => {
    if (pendingRole) {
      setUserRole({
        user_email: user.email,
        new_role: pendingRole,
      });
    }
    setShowConfirmModal(false);
    setPendingRole(null);
  };

  const getDisplayValue = () => {
    if (currentUser?.role === UserRole.ADMIN) {
      return user.role === UserRole.ADMIN ? UserRole.ADMIN : UserRole.BASIC;
    }
    return user.role;
  };

  return (
    <>
      <Select
        value={getDisplayValue()}
        onValueChange={handleChange}
        disabled={isSettingRole}
      >
        <SelectTrigger data-testid={`user-role-dropdown-trigger-${user.email}`}>
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {currentUser?.role === UserRole.ADMIN ? (
            // Admin view: only "Admin" and "Not Admin" options
            <>
              <SelectItem
                key={UserRole.ADMIN}
                value={UserRole.ADMIN}
                data-testid={`user-role-dropdown-${UserRole.ADMIN}`}
                title={INVALID_ROLE_HOVER_TEXT[UserRole.ADMIN] ?? ""}
                data-tooltip-delay="0"
              >
                Admin
              </SelectItem>
              <SelectItem
                key={UserRole.BASIC} // Map "Not Admin" to BASIC for backend
                value={UserRole.BASIC}
                data-testid={`user-role-dropdown-not-admin`}
                title={INVALID_ROLE_HOVER_TEXT[UserRole.BASIC] ?? ""}
                data-tooltip-delay="0"
              >
                Not Admin
              </SelectItem>
            </>
          ) : (
            // Non-admin view: show only "Basic" and "Team Admin" if current user is TEAM_ADMIN
            (Object.entries(USER_ROLE_LABELS) as [UserRole, string][])
              .filter(([role]) => {
                if (currentUser?.role === UserRole.TEAM_ADMIN) {
                  return role === UserRole.BASIC || role === UserRole.TEAM_ADMIN;
                }
                // For other non-admin users, apply existing filtering logic
                const isNotVisibleRole =
                  (!isPaidEnterpriseFeaturesEnabled &&
                    role === UserRole.GLOBAL_CURATOR) ||
                  role === UserRole.CURATOR ||
                  role === UserRole.LIMITED ||
                  role === UserRole.SLACK_USER ||
                  role === UserRole.EXT_PERM_USER; // Always hide EXT_PERM_USER

                return !isNotVisibleRole;
              })
              .map(([role, label]) => (
                <SelectItem
                  key={role}
                  onClick={() => {
                    console.log("clicked");
                  }}
                  value={role}
                  data-testid={`user-role-dropdown-${role}`}
                  title={INVALID_ROLE_HOVER_TEXT[role] ?? ""}
                  data-tooltip-delay="0"
                >
                  {label}
                </SelectItem>
              ))
          )}
        </SelectContent>
      </Select>
      {showConfirmModal && (
        <GenericConfirmModal
          title="Change Curator Role"
          message={`Warning: Switching roles from Curator to ${
            USER_ROLE_LABELS[pendingRole as UserRole] ??
            USER_ROLE_LABELS[user.role]
          } will remove their status as individual curators from all groups.`}
          confirmText={`Switch Role to ${
            USER_ROLE_LABELS[pendingRole as UserRole] ??
            USER_ROLE_LABELS[user.role]
          }`}
          onClose={() => setShowConfirmModal(false)}
          onConfirm={handleConfirm}
        />
      )}
    </>
  );
};

export default UserRoleDropdown;
