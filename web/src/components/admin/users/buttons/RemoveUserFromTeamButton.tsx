import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { PopupSpec } from "@/components/admin/connectors/Popup";
import { GenericConfirmModal as ConfirmModal } from "@/components/modals/GenericConfirmModal";
import { removeUserFromTeam } from "@/lib/admin/teams/teamMutationFetcher"; // This will be a new fetcher
import { UserMinus } from "lucide-react";

interface RemoveUserFromTeamButtonProps {
  userId: string;
  onSuccess: () => void;
  onError: (errorMsg: string) => void;
  className?: string;
}

export const RemoveUserFromTeamButton: React.FC<RemoveUserFromTeamButtonProps> = ({
  userId,
  onSuccess,
  onError,
  className,
}) => {
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleRemove = async () => {
    setIsSubmitting(true);
    try {
      const response = await removeUserFromTeam(userId);
      if (response.ok) {
        onSuccess();
      } else {
        const errorMsg = await response.text();
        onError(errorMsg);
      }
    } catch (error) {
      onError(error instanceof Error ? error.message : "An unknown error occurred");
    } finally {
      setIsSubmitting(false);
      setIsConfirmModalOpen(false);
    }
  };

  return (
    <>
      <Button
        variant="ghost"
        className={className}
        onClick={() => setIsConfirmModalOpen(true)}
        disabled={isSubmitting}
      >
        <UserMinus className="mr-2 h-4 w-4" />
        <span>Remove from Team</span>
      </Button>

      {isConfirmModalOpen && (
        <ConfirmModal
          title="Remove User from Team"
          message={`Are you sure you want to remove this user from the team?`}
          onConfirm={handleRemove}
          onClose={() => setIsConfirmModalOpen(false)}
        />
      )}
    </>
  );
};
