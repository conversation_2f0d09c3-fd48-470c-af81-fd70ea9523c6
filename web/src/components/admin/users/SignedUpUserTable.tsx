import {
  type User,
  UserRole,
  InvitedUserSnapshot,
  USER_ROLE_LABELS,
  USER_STATUS_LABELS,
} from "@/lib/types";
import { useState } from "react";
import CenteredPageSelector from "./CenteredPageSelector";
import { PopupSpec } from "@/components/admin/connectors/Popup";
import {
  Table,
  TableHead,
  TableRow,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import { TableHeader } from "@/components/ui/table";
import UserRoleDropdown from "./buttons/UserRoleDropdown";
import DeleteUserButton from "./buttons/DeleteUserButton";
import DeactivateUserButton from "./buttons/DeactivateUserButton";
import UninviteUserButton from "./buttons/UninviteUserButton";
import { RemoveUserFromTeamButton } from "./buttons/RemoveUserFromTeamButton"; // Import the new button
import usePaginatedFetch from "@/hooks/usePaginatedFetch";
import { ThreeDotsLoader } from "@/components/Loading";
import { ErrorCallout } from "@/components/ErrorCallout";
import { InviteUserButton } from "./buttons/InviteUserButton";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { RefreshCcw } from "lucide-react";
import { useUser } from "@/components/user/UserProvider";
import { LeaveOrganizationButton } from "./buttons/LeaveOrganizationButton";
import { NEXT_PUBLIC_CLOUD_ENABLED } from "@/lib/constants";
import ResetPasswordModal from "./ResetPasswordModal";
import {
  MoreHorizontal,
  LogOut,
  UserMinus,
  UserX,
  KeyRound,
} from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

const ITEMS_PER_PAGE = 10;
const PAGES_PER_BATCH = 2;

interface Props {
  invitedUsers: InvitedUserSnapshot[];
  setPopup: (spec: PopupSpec) => void;
  q: string;
  invitedUsersMutate: () => void;
  currentTeamId?: number; // Add currentTeamId prop
}

interface ActionMenuProps {
  user: User;
  currentUser: User | null;
  setPopup: (spec: PopupSpec) => void;
  refresh: () => void;
  invitedUsersMutate: () => void;
  handleResetPassword: (user: User) => void;
}

const SignedUpUserTable = ({
  invitedUsers,
  setPopup,
  q = "",
  invitedUsersMutate,
}: Props) => {
  const [filters, setFilters] = useState<{
    status?: string;
    roles?: UserRole[];
  }>({});

  const [selectedRoles, setSelectedRoles] = useState<UserRole[]>([]);
  const [resetPasswordUser, setResetPasswordUser] = useState<User | null>(null);

  const {
    currentPageData: pageOfUsers,
    isLoading,
    error,
    currentPage,
    totalPages,
    goToPage,
    refresh,
  } = usePaginatedFetch<User>({
    itemsPerPage: ITEMS_PER_PAGE,
    pagesPerBatch: PAGES_PER_BATCH,
    endpoint: "/api/manage/users/accepted",
    query: q,
    filter: filters,
  });

  const { user: currentUser } = useUser();

  // Filter out admin users if current user is a team_admin
  const filteredPageOfUsers = currentUser?.role === UserRole.TEAM_ADMIN
    ? pageOfUsers?.filter((u) => u.role !== UserRole.ADMIN)
    : pageOfUsers;

  if (error) {
    return (
      <ErrorCallout
        errorTitle="Error loading users"
        errorMsg={error?.message}
      />
    );
  }

  const handlePopup = (message: string, type: "success" | "error") => {
    if (type === "success") refresh();
    setPopup({ message, type });
  };

  const onRoleChangeSuccess = () =>
    handlePopup("User role updated successfully!", "success");
  const onRoleChangeError = (errorMsg: string) =>
    handlePopup(`Unable to update user role - ${errorMsg}`, "error");

  const toggleRole = (roleEnum: UserRole) => {
    setFilters((prev) => {
      const currentRoles = prev.roles || [];
      let newRoles: UserRole[];

      if (currentUser?.role === UserRole.ADMIN) {
        if (roleEnum === UserRole.ADMIN) {
          // Toggle Admin role
          newRoles = currentRoles.includes(UserRole.ADMIN)
            ? currentRoles.filter((r) => r !== UserRole.ADMIN)
            : [...currentRoles, UserRole.ADMIN];
        } else {
          // Toggle "Not Admin" (represented by BASIC)
          // When "Not Admin" is selected, we want to filter for all non-admin roles
          const nonAdminRoles: UserRole[] = [
            UserRole.BASIC,
            UserRole.TEAM_ADMIN,
            UserRole.CURATOR,
            UserRole.GLOBAL_CURATOR,
            UserRole.LIMITED,
            UserRole.EXT_PERM_USER,
          ];
          const isNotAdminSelected = currentRoles.some((r) => nonAdminRoles.includes(r));

          if (isNotAdminSelected) {
            // If "Not Admin" is currently selected, deselect all non-admin roles
            newRoles = currentRoles.filter((r) => r === UserRole.ADMIN);
          } else {
            // If "Not Admin" is not selected, add all non-admin roles
            newRoles = [...currentRoles.filter((r) => r === UserRole.ADMIN), ...nonAdminRoles];
          }
        }
      } else {
        // Non-admin view: toggle normally
        newRoles = currentRoles.includes(roleEnum)
          ? currentRoles.filter((r) => r !== roleEnum)
          : [...currentRoles, roleEnum];
      }

      // Update selectedRoles for display purposes
      const displayRoles: UserRole[] = [];
      if (newRoles.includes(UserRole.ADMIN)) {
        displayRoles.push(UserRole.ADMIN);
      }
      if (newRoles.some(r => r !== UserRole.ADMIN && r !== UserRole.SLACK_USER)) {
        displayRoles.push(UserRole.BASIC); // Use BASIC to represent "Not Admin" in display
      }
      setSelectedRoles(displayRoles);

      return {
        ...prev,
        roles: newRoles,
      };
    });
  };

  const removeRole = (roleEnum: UserRole) => {
    setFilters((prev) => {
      const currentRoles = prev.roles || [];
      let newRoles: UserRole[];

      if (currentUser?.role === UserRole.ADMIN) {
        if (roleEnum === UserRole.ADMIN) {
          // Remove Admin role
          newRoles = currentRoles.filter((r) => r !== UserRole.ADMIN);
        } else {
          // Remove "Not Admin" (represented by BASIC)
          newRoles = currentRoles.filter((r) => r === UserRole.ADMIN);
        }
      } else {
        // Non-admin view: remove normally
        newRoles = currentRoles.filter((r) => r !== roleEnum);
      }

      // Update selectedRoles for display purposes
      const displayRoles: UserRole[] = [];
      if (newRoles.includes(UserRole.ADMIN)) {
        displayRoles.push(UserRole.ADMIN);
      }
      if (newRoles.some(r => r !== UserRole.ADMIN && r !== UserRole.SLACK_USER)) {
        displayRoles.push(UserRole.BASIC); // Use BASIC to represent "Not Admin" in display
      }
      setSelectedRoles(displayRoles);

      return {
        ...prev,
        roles: newRoles,
      };
    });
  };

  const handleResetPassword = (user: User) => {
    setResetPasswordUser(user);
  };

  // --------------
  // Render Functions
  // --------------

  const renderFilters = () => (
    <>
      <div className="flex items-center gap-4 py-4">
        <Select
          value={filters.status || "all"}
          onValueChange={(selectedStatus) =>
            setFilters((prev) => {
              if (selectedStatus === "all") {
                const { status, ...rest } = prev;
                return rest;
              }
              return {
                ...prev,
                status: selectedStatus,
              };
            })
          }
        >
          <SelectTrigger className="w-[260px] h-[34px] bg-neutral">
            <SelectValue>
              {filters.status
                ? USER_STATUS_LABELS[filters.status] || filters.status
                : "All Statuses"}
            </SelectValue>
          </SelectTrigger>
          <SelectContent className="bg-background-50">
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="pending_assignment">Pending Assignment</SelectItem>
            <SelectItem value="ready_to_signup">Ready to Signup</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
          </SelectContent>
        </Select>
        <Select value="roles">
          <SelectTrigger className="w-[260px] h-[34px] bg-neutral">
            <SelectValue>
              {selectedRoles.length
                ? `${selectedRoles.length} role(s) selected`
                : "All Roles"}
            </SelectValue>
          </SelectTrigger>
          <SelectContent className="bg-background-50">
            {currentUser?.role === UserRole.ADMIN ? (
              // Admin view: only "Admin" and "Not Admin"
              <>
                <div
                  className="flex items-center space-x-2 px-2 py-1.5 cursor-pointer hover:bg-background-200"
                  onClick={() => toggleRole(UserRole.ADMIN)}
                >
                  <input
                    type="checkbox"
                    checked={selectedRoles.includes(UserRole.ADMIN) || false}
                    onChange={(e) => e.stopPropagation()}
                  />
                  <label className="text-sm font-normal">Admin</label>
                </div>
                <div
                  className="flex items-center space-x-2 px-2 py-1.5 cursor-pointer hover:bg-background-200"
                  onClick={() => toggleRole(UserRole.BASIC)} // Use BASIC to represent "Not Admin" for filtering
                >
                  <input
                    type="checkbox"
                    checked={
                      selectedRoles.some((r) => r !== UserRole.ADMIN && r !== UserRole.SLACK_USER) || false
                    } // Check if any non-admin role is selected
                    onChange={(e) => e.stopPropagation()}
                  />
                  <label className="text-sm font-normal">Not Admin</label>
                </div>
              </>
            ) : (
              // Non-admin view: show all relevant roles
              Object.entries(USER_ROLE_LABELS)
                .filter(([role]) =>
                  [UserRole.TEAM_ADMIN, UserRole.BASIC].includes(role as UserRole)
                )
                .map(([role, label]) => (
                  <div
                    key={role}
                    className="flex items-center space-x-2 px-2 py-1.5 cursor-pointer hover:bg-background-200"
                    onClick={() => toggleRole(role as UserRole)}
                  >
                    <input
                      type="checkbox"
                      checked={selectedRoles.includes(role as UserRole) || false}
                      onChange={(e) => e.stopPropagation()}
                    />
                    <label className="text-sm font-normal">{label}</label>
                  </div>
                ))
            )}
          </SelectContent>
        </Select>
      </div>
      <div className="flex gap-2 py-1">
        {selectedRoles.map((role) => (
          <button
            key={role}
            className="border border-background-300 bg-neutral p-1 rounded text-sm hover:bg-background-200"
            onClick={() => removeRole(role)}
            style={{ padding: "2px 8px" }}
          >
            <span>
              {role === UserRole.ADMIN ? "Admin" : "Not Admin"}
            </span>
            <span className="ml-3">&times;</span>
          </button>
        ))}
      </div>
    </>
  );

  // Only allow editing for basic or team_admin users if current user is a team_admin
  const canEditUser = (targetUser: User) => {
    if (currentUser?.role === UserRole.TEAM_ADMIN) {
      return targetUser.role === UserRole.BASIC || targetUser.role === UserRole.TEAM_ADMIN;
    }
    return true;
  };

  const renderUserRoleDropdown = (user: User) => {
    if (user.role === UserRole.SLACK_USER) {
      return <p className="ml-2">Slack User</p>;
    }
    if (!canEditUser(user)) {
      // If current user is ADMIN, display simplified role, otherwise display full label
      if (currentUser?.role === UserRole.ADMIN) {
        const displayRole = user.role === UserRole.ADMIN ? "Admin" : "Not Admin";
        return <span>{displayRole}</span>;
      }
      // For non-admin users, display the specific role label
      return <span>{USER_ROLE_LABELS[user.role]}</span>;
    }

    // For ADMIN users, the dropdown should only show "Admin" and "Not Admin" options
    // The UserRoleDropdown component itself needs to handle this filtering based on currentUser.role
    return (
      <UserRoleDropdown
        user={user}
        onSuccess={onRoleChangeSuccess}
        onError={onRoleChangeError}
      />
    );
  };

    const ActionMenu: React.FC<ActionMenuProps> = ({
    user,
    currentUser,
    setPopup,
    refresh,
    invitedUsersMutate,
    handleResetPassword,
  }) => {
    const buttonClassName = "w-full justify-start";

    return (
      <Popover>
        <PopoverTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-48">
          <div className="grid gap-2">
            {NEXT_PUBLIC_CLOUD_ENABLED && user.id === currentUser?.id ? (
              <LeaveOrganizationButton
                user={user}
                setPopup={setPopup}
                mutate={refresh}
                className={buttonClassName}
              >
                <LogOut className="mr-2 h-4 w-4" />
                <span>Leave Organization</span>
              </LeaveOrganizationButton>
            ) : (
              <>
                {currentUser?.role === UserRole.TEAM_ADMIN ? (
                  // Team Admin view: show "Remove from Team" or "Uninvite User"
                  <>
                      <RemoveUserFromTeamButton
                        userId={user.id}
                        onSuccess={() => {
                          setPopup({ type: "success", message: "User successfully removed from team!" });
                          refresh();
                        }}
                        onError={(errorMsg: string) => {
                          setPopup({ type: "error", message: `Failed to remove user: ${errorMsg}` });
                        }}
                        className={buttonClassName}
                      />
                   
                  </>
                ) : (
                  // Global Admin view or other roles: show original Deactivate/Delete/Uninvite
                  <>
                    {user.status === "active" && (
                      <>
                        <DeactivateUserButton
                          user={user}
                          deactivate={true}
                          setPopup={setPopup}
                          mutate={refresh}
                          className={buttonClassName}
                        >
                          <UserX className="mr-2 h-4 w-4" />
                          <span>Deactivate User</span>
                        </DeactivateUserButton>
                        {user.password_configured && (
                          <Button
                            variant="ghost"
                            className={buttonClassName}
                            onClick={() => handleResetPassword(user)}
                          >
                            <KeyRound className="mr-2 h-4 w-4" />
                            <span>Reset Password</span>
                          </Button>
                        )}
                      </>
                    )}
                    {user.status === "inactive" && (
                      <DeactivateUserButton
                        user={user}
                        deactivate={false}
                        setPopup={setPopup}
                        mutate={refresh}
                        className={buttonClassName}
                      >
                        <UserX className="mr-2 h-4 w-4" />
                        <span>Activate User</span>
                      </DeactivateUserButton>
                    )}
                    {user.status === "inactive" && (
                      <DeleteUserButton
                        user={user}
                        setPopup={setPopup}
                        mutate={refresh}
                        className={buttonClassName}
                      >
                        <UserMinus className="mr-2 h-4 w-4" />
                        <span>Delete User</span>
                      </DeleteUserButton>
                    )}
                    {(user.status === "pending_assignment" || user.status === "ready_to_signup") && (
                      <UninviteUserButton
                        user={user}
                        setPopup={setPopup}
                        mutate={refresh}
                        className={buttonClassName}
                      >
                        <UserMinus className="mr-2 h-4 w-4" />
                        <span>Uninvite User</span>
                      </UninviteUserButton>
                    )}
                  </>
                )}
              </>
            )}
          </div>
        </PopoverContent>
      </Popover>
    );
  };

  const renderActionButtons = (user: User) => {
    if (!canEditUser(user)) return null;
    return <ActionMenu user={user} currentUser={currentUser} setPopup={setPopup} refresh={refresh} invitedUsersMutate={invitedUsersMutate} handleResetPassword={setResetPasswordUser} />;
  };

  return (
    <>
      {renderFilters()}
      <Table className="overflow-visible">
        <TableHeader>
          <TableRow>
            <TableHead>Email</TableHead>
            <TableHead className="text-center">Role</TableHead>
            <TableHead className="text-center">Status</TableHead>
            <TableHead>
              <div className="flex">
                <div className="ml-auto">Actions</div>
              </div>
            </TableHead>
          </TableRow>
        </TableHeader>
        {isLoading ? (
          <TableBody>
            <TableRow>
              <TableCell colSpan={4} className="text-center">
                <ThreeDotsLoader />
              </TableCell>
            </TableRow>
          </TableBody>
        ) : (
          <TableBody>
            {!filteredPageOfUsers?.length ? (
              <TableRow>
                <TableCell colSpan={4} className="text-center">
                  <p className="pt-4 pb-4">
                    {filters.roles?.length || filters.status
                      ? "No users found matching your filters"
                      : `No users found matching "${q}"`}
                  </p>
                </TableCell>
              </TableRow>
            ) : (
              filteredPageOfUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>{user.email}</TableCell>
                  <TableCell className="w-[180px]">
                    {renderUserRoleDropdown(user)}
                  </TableCell>
                  <TableCell className="text-center w-[140px]">
                    <i>{USER_STATUS_LABELS[user.status || ""] || user.status || "Unknown"}</i>
                  </TableCell>
                  <TableCell className="text-right  w-[300px] ">
                    {renderActionButtons(user)}
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        )}
      </Table>
      {totalPages > 1 && (
        <CenteredPageSelector
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={goToPage}
        />
      )}
      {resetPasswordUser && (
        <ResetPasswordModal
          user={resetPasswordUser}
          onClose={() => setResetPasswordUser(null)}
          setPopup={setPopup}
        />
      )}
    </>
  );
};

export default SignedUpUserTable;
