"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { UserRole, USER_ROLE_LABELS } from "@/lib/types";
import { FiPlus, FiTrash2 } from "react-icons/fi";
import { usePaidEnterpriseFeaturesEnabled } from "@/components/settings/usePaidEnterpriseFeaturesEnabled";
import { getCurrentUser } from "@/lib/user";
import { useUser } from "@/components/user/UserProvider";

const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

interface UserInvite {
  email: string;
  role: UserRole;
}

interface InviteUsersFormProps {
  onSuccess: () => void;
  onFailure: (res: Response) => void;
}

const InviteUsersForm = ({ onSuccess, onFailure }: InviteUsersFormProps) => {
  const [users, setUsers] = useState<UserInvite[]>([
    { email: "", role: UserRole.BASIC },
  ]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);
  const { user: currentUser } = useUser();
  const isPaidEnterpriseFeaturesEnabled = usePaidEnterpriseFeaturesEnabled();

  // Get available roles for the dropdown
  const getAvailableRoles = () => {
    if (currentUser?.role === UserRole.ADMIN) {
      // Admin users can only invite other admins or basic users (represented as "not_admin")
      return [
        [UserRole.ADMIN, USER_ROLE_LABELS[UserRole.ADMIN]],
        [UserRole.BASIC, "Not Admin"], // Map BASIC to "Not Admin" for display
      ];
    } else if (currentUser?.role === UserRole.TEAM_ADMIN) {
      // Team admin can invite team admin and basic only
      return Object.entries(USER_ROLE_LABELS).filter(([role]) =>
        [UserRole.TEAM_ADMIN, UserRole.BASIC].includes(role as UserRole)
      );
    }

    // Default: return nothing or limit further if needed
    return [];
  };


  const addUser = () => {
    setUsers([...users, { email: "", role: UserRole.BASIC }]);
    setErrors([...errors, ""]);
  };

  const removeUser = (index: number) => {
    if (users.length > 1) {
      const newUsers = users.filter((_, i) => i !== index);
      const newErrors = errors.filter((_, i) => i !== index);
      setUsers(newUsers);
      setErrors(newErrors);
    }
  };

  const updateUser = (index: number, field: keyof UserInvite, value: string) => {
    const newUsers = [...users];
    newUsers[index] = { ...newUsers[index], [field]: value };
    setUsers(newUsers);

    // Clear error for this field when user starts typing
    if (field === "email") {
      const newErrors = [...errors];
      newErrors[index] = "";
      setErrors(newErrors);
    }
  };

  const validateForm = () => {
    const newErrors: string[] = [];
    let isValid = true;

    users.forEach((user, index) => {
      if (!user.email.trim()) {
        newErrors[index] = "Email is required";
        isValid = false;
      } else if (!EMAIL_REGEX.test(user.email)) {
        newErrors[index] = "Invalid email format";
        isValid = false;
      } else if (!user.role) {
        newErrors[index] = "Role is required";
        isValid = false;
      } else {
        newErrors[index] = "";
      }
    });

    // Check for duplicate emails
    const emailCounts = users.reduce((acc, user) => {
      if (user.email.trim()) {
        acc[user.email.toLowerCase()] = (acc[user.email.toLowerCase()] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    users.forEach((user, index) => {
      if (user.email.trim() && emailCounts[user.email.toLowerCase()] > 1) {
        newErrors[index] = "Duplicate email address";
        isValid = false;
      }
    });

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch("/api/manage/admin/users", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          users: users.map((user) => ({
            email: user.email.trim(),
            role: user.role,
          })),
        }),
      });

      if (response.ok) {
        onSuccess();
      } else {
        onFailure(response);
      }
    } catch (error) {
      console.error("Error inviting users:", error);
      // Create a mock response for the error case
      const mockResponse = new Response(
        JSON.stringify({ detail: "Network error occurred" }),
        { status: 500 }
      );
      onFailure(mockResponse);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex flex-col gap-y-4">
      <div className="space-y-3">
        {users.map((user, index) => (
          <div key={index} className="flex gap-x-3 items-start">
            <div className="flex-1">
              <Input
                type="email"
                placeholder="Enter email address"
                value={user.email}
                onChange={(e) => updateUser(index, "email", e.target.value)}
                className={errors[index] ? "border-red-500" : ""}
              />
              {errors[index] && (
                <p className="text-red-500 text-sm mt-1">{errors[index]}</p>
              )}
            </div>
            <div className="w-48">
              <Select
                value={user.role}
                onValueChange={(value) =>
                  updateUser(index, "role", value as UserRole)
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {getAvailableRoles().map(([role, label]) => (
                    <SelectItem key={role} value={role}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => removeUser(index)}
              disabled={users.length === 1}
              className="px-2"
            >
              <FiTrash2 className="h-4 w-4" />
            </Button>
          </div>
        ))}
      </div>

      <div className="flex flex-col items-center gap-y-4">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={addUser}
          className="flex items-center gap-x-2"
        >
          <FiPlus className="h-4 w-4" />
          Add User
        </Button>

        <Button
          type="submit"
          onClick={handleSubmit}
          disabled={
            isSubmitting ||
            users.every((user) => !user.email.trim()) ||
            users.some((user) => !user.email.trim() || !user.role)
          }
          className="px-8"
        >
          {isSubmitting ? "Submitting..." : "Submit"}
        </Button>
      </div>
    </div>
  );
};

export default InviteUsersForm;
