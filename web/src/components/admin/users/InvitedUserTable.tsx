import { useState } from "react";
import { PopupSpec } from "@/components/admin/connectors/Popup";
import {
  Table,
  TableHead,
  TableRow,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import CenteredPageSelector from "./CenteredPageSelector";
import { ThreeDotsLoader } from "@/components/Loading";
import { InvitedUserSnapshot, USER_ROLE_LABELS, USER_STATUS_LABELS, UserRole } from "@/lib/types";
import { TableHeader } from "@/components/ui/table";
import { InviteUserButton } from "./buttons/InviteUserButton";
import { ErrorCallout } from "@/components/ErrorCallout";
import { FetchError } from "@/lib/fetcher";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useUser } from "@/components/user/UserProvider";

const USERS_PER_PAGE = 10;

interface Props {
  users: InvitedUserSnapshot[];
  setPopup: (spec: PopupSpec) => void;
  mutate: () => void;
  error: FetchError | null;
  isLoading: boolean;
  q: string;
}



const InvitedUserTable = ({
  users,
  setPopup,
  mutate,
  error,
  isLoading,
  q,
}: Props) => {
  const [currentPageNum, setCurrentPageNum] = useState<number>(1);
  const [selectedRoles, setSelectedRoles] = useState<UserRole[]>([]);
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const { user: currentUser } = useUser();

  const toggleRole = (roleEnum: UserRole) => {
    setSelectedRoles((prev) => {
      const currentRoles = prev;
      let newRoles: UserRole[];

      if (currentUser?.role === UserRole.ADMIN) {
        if (roleEnum === UserRole.ADMIN) {
          newRoles = currentRoles.includes(UserRole.ADMIN)
            ? currentRoles.filter((r) => r !== UserRole.ADMIN)
            : [...currentRoles, UserRole.ADMIN];
        } else {
          const nonAdminRoles: UserRole[] = [
            UserRole.BASIC,
            UserRole.TEAM_ADMIN,
            UserRole.CURATOR,
            UserRole.GLOBAL_CURATOR,
            UserRole.LIMITED,
            UserRole.EXT_PERM_USER,
          ];
          const isNotAdminSelected = currentRoles.some((r) => nonAdminRoles.includes(r));

          if (isNotAdminSelected) {
            newRoles = currentRoles.filter((r) => r === UserRole.ADMIN);
          } else {
            newRoles = [...currentRoles.filter((r) => r === UserRole.ADMIN), ...nonAdminRoles];
          }
        }
      } else {
        newRoles = currentRoles.includes(roleEnum)
          ? currentRoles.filter((r) => r !== roleEnum)
          : [...currentRoles, roleEnum];
      }

      // For display purposes, consolidate non-admin roles into a single BASIC entry
      const displayRoles: UserRole[] = [];
      if (newRoles.includes(UserRole.ADMIN)) {
        displayRoles.push(UserRole.ADMIN);
      }
      if (newRoles.some(r => r !== UserRole.ADMIN && r !== UserRole.SLACK_USER)) {
        displayRoles.push(UserRole.BASIC); // Use BASIC to represent "Not Admin" in display
      }
      return displayRoles;
    });
  };

  const removeRole = (roleEnum: UserRole) => {
    setSelectedRoles((prev) => {
      const currentRoles = prev;
      let newRoles: UserRole[];

      if (currentUser?.role === UserRole.ADMIN) {
        if (roleEnum === UserRole.ADMIN) {
          newRoles = currentRoles.filter((r) => r !== UserRole.ADMIN);
        } else {
          newRoles = currentRoles.filter((r) => r === UserRole.ADMIN);
        }
      } else {
        newRoles = currentRoles.filter((r) => r !== roleEnum);
      }

      // For display purposes, consolidate non-admin roles into a single BASIC entry
      const displayRoles: UserRole[] = [];
      if (newRoles.includes(UserRole.ADMIN)) {
        displayRoles.push(UserRole.ADMIN);
      }
      if (newRoles.some(r => r !== UserRole.ADMIN && r !== UserRole.SLACK_USER)) {
        displayRoles.push(UserRole.BASIC); // Use BASIC to represent "Not Admin" in display
      }
      return displayRoles;
    });
  };

  if (!users.length)
    return <p>Users that have been invited will show up here</p>;

  const totalPages = Math.ceil(users.length / USERS_PER_PAGE);

  // Filter users based on the search query, role, and status
  let filteredUsers = users;
  if (q) {
    filteredUsers = filteredUsers.filter((user) => user.email.includes(q));
  }
  if (selectedRoles.length > 0) {
    filteredUsers = filteredUsers.filter((user) => {
      if (!user.role) return false; // Skip if user has no role

      if (selectedRoles.includes(UserRole.ADMIN) && user.role === UserRole.ADMIN) {
        return true;
      }
      // If "Not Admin" is selected, check if the user's role is any non-admin role
      if (selectedRoles.includes(UserRole.BASIC) && user.role !== UserRole.ADMIN) {
        return true;
      }
      return false;
    });
  }
  if (statusFilter !== "all") {
    filteredUsers = filteredUsers.filter(
      (user) => user.status === statusFilter
    );
  }

  // Get the current page of users
  const currentPageOfUsers = filteredUsers.slice(
    (currentPageNum - 1) * USERS_PER_PAGE,
    currentPageNum * USERS_PER_PAGE
  );

  if (isLoading) {
    return <ThreeDotsLoader />;
  }

  if (error) {
    return (
      <ErrorCallout
        errorTitle="Error loading users"
        errorMsg={error?.info?.detail}
      />
    );
  }

  // Filter UI
  const renderFilters = () => (
    <div className="flex items-center gap-4 py-4">
      {/* Status Filter */}
      <Select
        value={statusFilter}
        onValueChange={setStatusFilter}
      >
        <SelectTrigger className="w-[220px] h-[34px] bg-neutral">
          <SelectValue>
            {statusFilter === "all"
              ? "All Statuses"
              : USER_STATUS_LABELS[statusFilter] || statusFilter}
          </SelectValue>
        </SelectTrigger>
        <SelectContent className="bg-background-50">
          <SelectItem value="all">All Statuses</SelectItem>
          <SelectItem value="pending_assignment">Pending Assignment</SelectItem>
          <SelectItem value="ready_to_signup">Ready to Signup</SelectItem>
        </SelectContent>
      </Select>
      {/* Role Filter */}
      <Select value="roles">
        <SelectTrigger className="w-[260px] h-[34px] bg-neutral">
          <SelectValue>
            {selectedRoles.length
              ? `${selectedRoles.length} role(s) selected`
              : "All Roles"}
          </SelectValue>
        </SelectTrigger>
        <SelectContent className="bg-background-50">
          {currentUser?.role === UserRole.ADMIN ? (
            // Admin view: only "Admin" and "Not Admin"
            <>
              <div
                className="flex items-center space-x-2 px-2 py-1.5 cursor-pointer hover:bg-background-200"
                onClick={() => toggleRole(UserRole.ADMIN)}
              >
                <input
                  type="checkbox"
                  checked={selectedRoles.includes(UserRole.ADMIN)}
                  onChange={(e) => e.stopPropagation()}
                />
                <label className="text-sm font-normal">Admin</label>
              </div>
              <div
                className="flex items-center space-x-2 px-2 py-1.5 cursor-pointer hover:bg-background-200"
                onClick={() => toggleRole(UserRole.BASIC)} // Map "Not Admin" to BASIC for filtering
              >
                <input
                  type="checkbox"
                  checked={selectedRoles.some((r) => r !== UserRole.ADMIN)} // Check if any non-admin role is selected
                  onChange={(e) => e.stopPropagation()}
                />
                <label className="text-sm font-normal">Not Admin</label>
              </div>
            </>
          ) : (
            // Non-admin view: show all relevant roles
            Object.entries(USER_ROLE_LABELS)
              .filter(([role]) =>
                [UserRole.TEAM_ADMIN, UserRole.BASIC].includes(role as UserRole)
              )
              .map(([role, label]) => (
                <div
                  key={role}
                  className="flex items-center space-x-2 px-2 py-1.5 cursor-pointer hover:bg-background-200"
                  onClick={() => toggleRole(role as UserRole)}
                >
                  <input
                    type="checkbox"
                    checked={selectedRoles.includes(role as UserRole)}
                    onChange={(e) => e.stopPropagation()}
                  />
                  <label className="text-sm font-normal">{label}</label>
                </div>
              ))
          )}
        </SelectContent>
      </Select>
      {/* Selected Roles Chips */}
      <div className="flex gap-2 py-1">
        {selectedRoles.map((role) => (
          <button
            key={role}
            className="border border-background-300 bg-neutral p-1 rounded text-sm hover:bg-background-200"
            onClick={() => removeRole(role)}
            style={{ padding: "2px 8px" }}
          >
            <span>
              {role === UserRole.ADMIN ? "Admin" : "Not Admin"}
            </span>
            <span className="ml-3">&times;</span>
          </button>
        ))}
      </div>
    </div>
  );

  return (
    <>
      {renderFilters()}
      <Table className="overflow-visible">
        <TableHeader>
          <TableRow>
            <TableHead>Email</TableHead>
            <TableHead>Role</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>
              <div className="flex justify-end">Actions</div>
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {currentPageOfUsers.length ? (
            currentPageOfUsers.map((user) => (
              <TableRow key={user.email}>
                <TableCell>{user.email}</TableCell>
                <TableCell>
                  {currentUser?.role === UserRole.ADMIN
                    ? user.role === UserRole.ADMIN
                      ? "Admin"
                      : "Not Admin"
                    : user.role
                      ? USER_ROLE_LABELS[user.role]
                      : USER_ROLE_LABELS[UserRole.BASIC]}
                </TableCell>
                <TableCell>
                  {USER_STATUS_LABELS[user.status || ""] || user.status || "-"}
                </TableCell>
                <TableCell>
                  <div className="flex justify-end">
                    <InviteUserButton
                      user={user}
                      invited={true}
                      setPopup={setPopup}
                      mutate={mutate}
                    />
                  </div>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={3} className="h-24 text-center">
                {`No users found matching "${q}"`}
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      {totalPages > 1 ? (
        <CenteredPageSelector
          currentPage={currentPageNum}
          totalPages={totalPages}
          onPageChange={setCurrentPageNum}
        />
      ) : null}
    </>
  );
};

export default InvitedUserTable;
