"use client";
import { useEffect, useMemo, useState } from "react";
import { Combobox, ComboboxButton, ComboboxInput, ComboboxOption, ComboboxOptions } from "@headlessui/react";
import { getStoredTeamId, setStoredTeamId } from "@/lib/team";
import { mutate } from "swr";
import useSWR from "swr";
import { errorHandlingFetcher } from "@/lib/fetcher";
import { useUser } from "./user/UserProvider";
import { UserRole } from "@/lib/types";

type Option = { id: number; name: string };

export default function TeamSwitcher() {
  const { user } = useUser();
  const { data: teams } = useSWR<Option[]>("/api/user/teams", errorHandlingFetcher);
  const options: Option[] = useMemo(() => teams || [], [teams]);

  const initial = useMemo(() => {
    const stored = getStoredTeamId();
    if (stored !== null) {
      return stored;
    }
    if (options.length > 0) {
      return options[0].id;
    }
    return null;
  }, [options]);

  const [selectedId, setSelectedId] = useState<number | null>(initial);
  const [query, setQuery] = useState("");

  useEffect(() => {
    setSelectedId(initial);
  }, [initial]);

  const filtered = query
    ? options.filter((o) => o.name.toLowerCase().includes(query.toLowerCase()))
    : options;

  const selected = options.find((o) => o.id === selectedId) || null;

  const applyTeam = async (teamId: number) => {
    setStoredTeamId(teamId);
    // Hit a lightweight endpoint with the header so backend updates the cookie
    try {
      await fetch("/api/health", { headers: { "X-Current-Team-ID": String(teamId) } });
    } catch {
      // ignore network errors; cookie will still persist if reachable later
    }
    // Notify app so non-SWR data sources (e.g., persona context) can refresh immediately
    try {
      window.dispatchEvent(new CustomEvent("onyx-team-changed", { detail: { teamId } }));
    } catch {}
    // Invalidate SWR caches that depend on team
    mutate(() => true, undefined, { revalidate: true });
  };

  // Hide for admins, or when user has <= 1 team
  const isAdmin = user?.role === UserRole.ADMIN;
  if (isAdmin || !options || options.length <= 1) {
    return null;
  }

  return (
    <div className="relative inline-block w-44">
      <Combobox
        value={selected}
        onChange={(opt: Option) => {
          setSelectedId(opt.id);
          applyTeam(opt.id);
        }}
        onClose={() => setQuery("")}
      >
        <div className="relative">
          <ComboboxInput
            className="w-full rounded-md bg-background px-3 py-2 text-sm border border-border focus:outline-none"
            displayValue={(opt?: Option) => opt?.name || "Select team"}
            onChange={(e) => setQuery(e.target.value)}
          />
          <ComboboxButton className="absolute inset-y-0 right-0 flex items-center pr-2 text-text-500">
            ▾
          </ComboboxButton>
        </div>
        <ComboboxOptions className="absolute left-0 right-0 z-20 mt-1 max-h-60 w-full overflow-auto rounded-md border border-border bg-background shadow-lg empty:invisible">
          {filtered.map((opt) => (
            <ComboboxOption
              key={opt.id}
              value={opt}
              className="cursor-pointer select-none px-3 py-2 text-sm data-focus:bg-accent-background data-selected:text-primary-600"
            >
              {opt.name}
            </ComboboxOption>
          ))}
        </ComboboxOptions>
      </Combobox>
    </div>
  );
}


