import re
from datetime import datetime
from datetime import timezone

import jwt
from email_validator import EmailNotValidError
from email_validator import EmailUndeliverableError
from email_validator import validate_email
from fastapi import APIRouter
from fastapi import Body
from fastapi import Depends
from fastapi import HTT<PERSON>Exception
from fastapi import Query
from fastapi import Request
from fastapi import status
from fastapi.responses import JSONResponse
from psycopg2.errors import UniqueViolation
from pydantic import BaseModel
from pydantic import EmailStr
from sqlalchemy import Column
from sqlalchemy import desc
from sqlalchemy import select
from sqlalchemy import update
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

import uuid

import uuid

from ee.onyx.configs.app_configs import SUPER_USERS
from onyx.auth.email_utils import send_user_email_invite
from onyx.auth.invited_users import get_invited_users_with_roles
from onyx.auth.invited_users import write_invited_users_with_roles
from onyx.auth.noauth_user import fetch_no_auth_user
from onyx.auth.noauth_user import set_no_auth_user_preferences
from onyx.auth.schemas import User<PERSON><PERSON>
from onyx.auth.users import anonymous_user_enabled
from onyx.auth.users import current_admin_user
from onyx.auth.users import current_curator_or_admin_user
from onyx.auth.users import current_team_admin_or_admin_user
from onyx.auth.users import current_user
from onyx.auth.users import get_current_team_id_from_header
from onyx.auth.users import optional_user
from onyx.configs.app_configs import AUTH_TYPE
from onyx.configs.app_configs import DEV_MODE
from onyx.configs.app_configs import ENABLE_EMAIL_INVITES
from onyx.configs.app_configs import SESSION_EXPIRE_TIME_SECONDS
from onyx.configs.app_configs import VALID_EMAIL_DOMAINS
from onyx.configs.constants import AuthType
from onyx.configs.constants import FASTAPI_USERS_AUTH_COOKIE_NAME
from onyx.db.api_key import is_api_key_email_address
from onyx.db.auth import get_total_users_count
from onyx.db.engine import get_session
from onyx.db.models import AccessToken
from onyx.db.models import InvitedUser
from onyx.db.models import User
from onyx.db.models import User__UserGroup
from onyx.db.users import delete_user_from_db
from onyx.db.users import get_all_users
from onyx.db.users import get_page_of_filtered_users
from onyx.db.users import get_total_filtered_users_count
from onyx.db.users import get_user_by_email

from onyx.db.users import validate_user_role_update
from onyx.db.user_teams import _validate_user_status_for_team_assignment
from onyx.key_value_store.factory import get_kv_store
from onyx.server.documents.models import PaginatedReturn
from onyx.server.manage.models import AllUsersResponse
from onyx.server.manage.models import AutoScrollRequest
from onyx.server.manage.models import BulkUserInviteRequest
from onyx.server.manage.models import UserByEmail
from onyx.server.manage.models import UserInfo
from onyx.server.manage.models import UserInviteRequest
from onyx.server.manage.models import UserPreferences
from onyx.server.manage.models import UserRoleResponse
from onyx.server.manage.models import UserRoleUpdateRequest

from onyx.server.models import FullUserSnapshot
from onyx.server.models import InvitedUserSnapshot
from onyx.server.models import MinimalUserSnapshot
from onyx.server.models import UserIdSnapshot
from onyx.server.models import BulkInviteUserRequest
from onyx.server.utils import BasicAuthenticationError
from onyx.utils.logger import setup_logger
from onyx.utils.variable_functionality import fetch_ee_implementation_or_noop
from shared_configs.configs import MULTI_TENANT
from shared_configs.contextvars import get_current_tenant_id
from onyx.db.models import UserGroup
from onyx.db.models import User__UserGroup
from fastapi_users.password import PasswordHelper
import secrets

logger = setup_logger()
router = APIRouter()

USERS_PAGE_SIZE = 10

class UserTeamSnapshot(BaseModel):
    id: int
    name: str


@router.patch("/manage/set-user-role")
def set_user_role(
    user_role_update_request: UserRoleUpdateRequest,
    current_user: User = Depends(current_team_admin_or_admin_user),
    current_team_id: int = Depends(get_current_team_id_from_header),
    db_session: Session = Depends(get_session),
) -> None:
    user_to_update = get_user_by_email(
        email=user_role_update_request.user_email, db_session=db_session
    )
    if not user_to_update:
        raise HTTPException(status_code=404, detail="User not found")

    if current_user.role == UserRole.TEAM_ADMIN:
        user_to_update._current_team_id = current_team_id

    # Get current role based on new structure
    current_role = user_to_update.role
    requested_role = user_role_update_request.new_role
    if requested_role == current_role:
        return

    # This will raise an exception if the role update is invalid
    validate_user_role_update(
        requested_role=requested_role,
        current_role=current_role,
        acting_user_role=current_user.role,
    )

    if user_to_update.id == current_user.id:
        raise HTTPException(
            status_code=400,
            detail="User cannot demote themselves!",
        )

    if requested_role == UserRole.CURATOR:
        # Remove all curator db relationships before changing role
        fetch_ee_implementation_or_noop(
            "onyx.db.user_group",
            "remove_curator_status__no_commit",
        )(db_session, user_to_update)

    # Handle role transitions with new structure
    if current_user.role == UserRole.ADMIN:
        # Admin acting
        if current_role != UserRole.ADMIN and requested_role == UserRole.ADMIN:
            # Promoting to ADMIN
            # Remove all existing team associations
            db_session.query(User__UserGroup).filter(
                User__UserGroup.user_id == user_to_update.id
            ).delete()
            # Add the user to the global admin group (user_group_id = -1)
            admin_relationship = User__UserGroup(
                user_id=user_to_update.id,
                user_group_id=-1,
                role=UserRole.ADMIN,
                is_admin=True,
                is_curator=False
            )
            db_session.add(admin_relationship)
            user_to_update.is_admin = True
        elif current_role == UserRole.ADMIN and requested_role != UserRole.ADMIN:
            # Demoting a user from ADMIN to a non-admin role
            # Remove the user from the global admin group (user_group_id == -1)
            db_session.query(User__UserGroup).filter(
                User__UserGroup.user_id == user_to_update.id,
                User__UserGroup.user_group_id == -1
            ).delete()
            user_to_update.is_admin = False

    elif current_user.role == UserRole.TEAM_ADMIN:
        # Team Admin acting
        if requested_role == UserRole.ADMIN:
            raise HTTPException(
                status_code=403,
                detail="Team Admins cannot promote users to Global Admin role."
            )
        if current_role == UserRole.ADMIN and requested_role != UserRole.ADMIN:
            raise HTTPException(
                status_code=403,
                detail="Team Admins cannot demote Global Admins."
            )
        if requested_role not in [UserRole.BASIC, UserRole.TEAM_ADMIN]:
            raise HTTPException(
                status_code=403,
                detail="Team Admins can only assign 'basic' or 'team_admin' roles."
            )
        # For valid transitions, call _handle_role_transition
        _handle_role_transition(
            db_session=db_session,
            user_to_update=user_to_update,
            current_role=current_role,
            requested_role=requested_role,
            current_team_id=current_team_id
        )

    db_session.commit()


@router.get("/manage/users/accepted")
def list_accepted_users(
    q: str | None = Query(default=None),
    page_num: int = Query(0, ge=0),
    page_size: int = Query(10, ge=1, le=1000),
    roles: list[UserRole] = Query(default=[]),
    is_active: bool | None = Query(default=None),  # Keep for backward compatibility
    status: str | None = Query(default=None),  # New status filter
    user: User | None = Depends(current_team_admin_or_admin_user),
    current_team_id: int = Depends(get_current_team_id_from_header),
    db_session: Session = Depends(get_session),
) -> PaginatedReturn[FullUserSnapshot]:
    # Determine team filtering based on user role
    requesting_user_current_team_id_for_snapshot = None
    if user and user.role == UserRole.TEAM_ADMIN:
        # Team admin users can only see users in their current team
        if not current_team_id:
            # If team admin has no current team, return empty result
            return PaginatedReturn(
                items=[],
                total_items=0,
            )
        user_team_ids = [current_team_id]
        requesting_user_current_team_id_for_snapshot = current_team_id
    elif user and user.role == UserRole.ADMIN:
        # Global admins see all users, so no team filtering
        user_team_ids = None
        requesting_user_current_team_id_for_snapshot = None # Let snapshot handle admin role

    # If status filter is provided, we need to load all users and filter by computed status
    if status:
        from onyx.db.users import get_all_users
        from sqlalchemy.orm import selectinload

        # Get all users with user_groups and user_group_relationships loaded for status computation
        all_users_query = db_session.query(User).options(
            selectinload(User.user_groups),
            selectinload(User.user_group_relationships)
        )

        # Apply basic filters (email, roles)
        if q:
            all_users_query = all_users_query.filter(User.email.ilike(f"%{q}%"))
        if roles:
            all_users_query = all_users_query.filter(User.role.in_(roles))

        # Exclude API key users
        all_users_query = all_users_query.filter(
            ~User.email.endswith("@danswer-api-key.invalid")
        )

        all_users = all_users_query.all()

        # Filter by computed status
        filtered_users = [user for user in all_users if user.status == status]

        # Apply pagination
        total_accepted_users_count = len(filtered_users)
        start_idx = page_num * page_size
        end_idx = start_idx + page_size
        filtered_accepted_users = filtered_users[start_idx:end_idx]

    else:
        # Use existing filtering for backward compatibility
        filtered_accepted_users = get_page_of_filtered_users(
            db_session=db_session,
            page_size=page_size,
            page_num=page_num,
            email_filter_string=q,
            is_active_filter=is_active,
            roles_filter=roles,
            user_team_ids=user_team_ids,
        )

        total_accepted_users_count = get_total_filtered_users_count(
            db_session=db_session,
            email_filter_string=q,
            is_active_filter=is_active,
            roles_filter=roles,
        user_team_ids=user_team_ids,
        )

    if not filtered_accepted_users:
        logger.info("No users found")
        return PaginatedReturn(
            items=[],
            total_items=0,
        )

    return PaginatedReturn(
        items=[
            FullUserSnapshot.from_user_model(user, requesting_user_current_team_id_for_snapshot) for user in filtered_accepted_users
        ],
        total_items=total_accepted_users_count,
    )


@router.get("/manage/users/invited")
def list_invited_users(
    user: User | None = Depends(current_team_admin_or_admin_user),
    current_team_id: int = Depends(get_current_team_id_from_header),
    db_session: Session = Depends(get_session),
) -> list[dict]:
    # Determine team filtering based on user role
    user_team_ids = None
    if user and user.role == UserRole.TEAM_ADMIN:
        # Team admin users can only see invited users in their current team
        if not current_team_id:
            # If team admin has no current team, return empty result
            return []
        user_team_ids = [current_team_id]
        requesting_user_current_team_id_for_snapshot = current_team_id
    elif user and user.role == UserRole.ADMIN:
        # Global admins see all users, so no team filtering
        user_team_ids = None
        requesting_user_current_team_id_for_snapshot = None # Let snapshot handle admin role

    # Base query for invited users with their associated User records
    query = (
        db_session.query(InvitedUser, User)
        .join(User, InvitedUser.id == User.id)
    )

    # Apply team filtering for team_admin users
    if user_team_ids is not None:
        query = query.join(User__UserGroup, User__UserGroup.user_id == User.id).filter(
            User__UserGroup.user_group_id.in_(user_team_ids)
        )

    invites_and_users = query.all()
    results = []
    for invite, user_record in invites_and_users:
        # Pass requesting_user_current_team_id_for_snapshot to the snapshot to ensure role/status are computed correctly
        invited_snapshot = InvitedUserSnapshot.from_user_model(user_record, requesting_user_current_team_id_for_snapshot)
        results.append({
            "email": invited_snapshot.email,
            "role": invited_snapshot.role,
            "status": invited_snapshot.status,
            "invited_at": invite.invited_at,
        })
    return results


@router.get("/manage/users")
def list_all_users(
    q: str | None = None,
    accepted_page: int | None = None,
    slack_users_page: int | None = None,
    invited_page: int | None = None,
    include_api_keys: bool = False,
    requesting_user: User | None = Depends(current_curator_or_admin_user),
    current_team_id: int = Depends(get_current_team_id_from_header),
    db_session: Session = Depends(get_session),
) -> AllUsersResponse:
    requesting_user_current_team_id_for_snapshot = None
    if requesting_user and requesting_user.role == UserRole.TEAM_ADMIN:
        requesting_user_current_team_id_for_snapshot = current_team_id
    elif requesting_user and requesting_user.role == UserRole.ADMIN:
        requesting_user_current_team_id_for_snapshot = None # Let snapshot handle admin role

    users = [
        user
        for user in get_all_users(db_session, email_filter_string=q)
        if (include_api_keys or not is_api_key_email_address(user.email))
    ]

    slack_users = [user for user in users if user.role == UserRole.SLACK_USER]
    accepted_users = [user for user in users if user.role != UserRole.SLACK_USER]

    accepted_emails = {user.email for user in accepted_users}
    slack_users_emails = {user.email for user in slack_users}
    invited_users_data = get_invited_users_with_roles()
    if q:
        invited_users_data = [
            user_data for user_data in invited_users_data
            if re.search(r"{}".format(q), user_data["email"], re.I)
        ]

    accepted_count = len(accepted_emails)
    slack_users_count = len(slack_users_emails)
    invited_count = len(invited_users_data)

    # If any of q, accepted_page, or invited_page is None, return all users
    if accepted_page is None or invited_page is None or slack_users_page is None:
        return AllUsersResponse(
            accepted=[
                FullUserSnapshot.from_user_model(user, requesting_user_current_team_id_for_snapshot)
                for user in accepted_users
            ],
            slack_users=[
                FullUserSnapshot.from_user_model(user, requesting_user_current_team_id_for_snapshot)
                for user in slack_users
            ],
            invited=[
                InvitedUserSnapshot.from_user_model(
                    db_session.query(User).filter_by(email=user_data["email"]).first(),
                    requesting_user_current_team_id_for_snapshot
                )
                for user_data in invited_users_data
            ],
            accepted_pages=1,
            invited_pages=1,
            slack_users_pages=1,
        )

    # Otherwise, return paginated results
    return AllUsersResponse(
        accepted=[
            FullUserSnapshot.from_user_model(user, requesting_user_current_team_id_for_snapshot)
            for user in accepted_users
        ][accepted_page * USERS_PAGE_SIZE : (accepted_page + 1) * USERS_PAGE_SIZE],
        slack_users=[
            FullUserSnapshot.from_user_model(user, requesting_user_current_team_id_for_snapshot)
            for user in slack_users
        ][
            slack_users_page
            * USERS_PAGE_SIZE : (slack_users_page + 1)
            * USERS_PAGE_SIZE
        ],
        invited=[
            InvitedUserSnapshot.from_user_model(
                db_session.query(User).filter_by(email=user_data["email"]).first(),
                requesting_user_current_team_id_for_snapshot
            )
            for user_data in invited_users_data
        ]
        [
            invited_page * USERS_PAGE_SIZE : (invited_page + 1) * USERS_PAGE_SIZE
        ],
        accepted_pages=(accepted_count + USERS_PAGE_SIZE - 1) // USERS_PAGE_SIZE,
        invited_pages=(invited_count + USERS_PAGE_SIZE - 1) // USERS_PAGE_SIZE,
        slack_users_pages=(slack_users_count + USERS_PAGE_SIZE - 1) // USERS_PAGE_SIZE,
    )


@router.put("/manage/admin/users")
def bulk_invite_users(
    request_body: BulkInviteUserRequest,
    current_user: User | None = Depends(current_team_admin_or_admin_user),
    current_team_id: int = Depends(get_current_team_id_from_header),
    db_session: Session = Depends(get_session),
) -> int:
    """Invite users with roles"""

    tenant_id = get_current_tenant_id()

    if current_user is None:
        raise HTTPException(
            status_code=400,
            detail="Auth is disabled, cannot invite users"
        )

    users_data = request_body.users
    if not users_data:
        raise HTTPException(
            status_code=400,
            detail="Please provide a list of users with email and role."
        )

    # Get team admin's current team if the current user is a team admin
    team_admin_team = None
    if current_user.role == UserRole.TEAM_ADMIN:
        team_admin_team = current_team_id
        if not team_admin_team:
            raise HTTPException(
                status_code=400,
                detail="Team admin must have a current team assigned to invite users"
            )

    invited_count = 0
    for i, user_data in enumerate(users_data):
        if not user_data.email:
            raise HTTPException(
                status_code=422,
                detail=f"Email is required for user at index {i}"
            )

        if not user_data.role:
            raise HTTPException(
                status_code=422,
                detail=f"Role is required for user at index {i}"
            )

        # Validate role is a valid UserRole
        try:
            role_enum = UserRole(user_data.role)
        except ValueError:
            valid_roles = [role.value for role in UserRole]
            raise HTTPException(
                status_code=422,
                detail=f"Invalid role '{user_data.role}' at index {i}. Valid roles: {', '.join(valid_roles)}"
            )

        # Team admin restrictions
        current_user_role = current_user.role
        if current_user_role == UserRole.TEAM_ADMIN:
            if user_data.role not in [UserRole.BASIC.value, UserRole.TEAM_ADMIN.value]:
                raise HTTPException(
                    status_code=403,
                    detail=f"Team admins can only invite users with 'basic' or 'team_admin' roles. Cannot assign role '{user_data.role}' at index {i}."
                )

        email = user_data.email.strip().lower()
        role = UserRole(user_data.role)
        status = "ready_to_signup" if role == UserRole.ADMIN else "pending_assignment"

        # Check if user already exists
        existing_user = db_session.query(User).filter_by(email=email).first()

        if existing_user:
            if current_user_role == UserRole.TEAM_ADMIN and team_admin_team:
                # Check if the existing user is already part of the current team
                is_user_in_team = db_session.query(User__UserGroup).filter(
                    User__UserGroup.user_id == existing_user.id,
                    User__UserGroup.user_group_id == team_admin_team
                ).first() is not None

                if is_user_in_team:
                    # User is already in the team, skip and log
                    logger.info(f"User {email} is already in team {team_admin_team}. Skipping invitation.")
                    continue # Skip to the next user in the loop
                else:
                    # User exists but is not in the current team, add them to the team
                    _validate_user_status_for_team_assignment(existing_user) # Add this line
                    team_relationship = User__UserGroup(
                        user_id=existing_user.id,
                        user_group_id=team_admin_team,
                        role=role,
                        is_admin=False,
                        is_curator=False
                    )
                    db_session.add(team_relationship)
                    invited_count += 1
                    continue # Skip to the next user in the loop
            else:
                # For global admins or if no team context, if user exists, it's an error
                raise ValueError("User already exists or has been invited.")

        # If user does not exist, proceed with new user creation and invitation
        is_admin = role == UserRole.ADMIN  # Only Admin role gets is_admin=True
        new_user = User(
            email=email,
            is_active=False,
            is_admin=is_admin,
            hashed_password=None,
        )
        db_session.add(new_user)
        db_session.flush()

        new_invite = InvitedUser(
            id=new_user.id,
            status=status,
        )
        db_session.add(new_invite)

        # Handle role-specific setup
        if role == UserRole.ADMIN:
            # Add to global admin group
            admin_relationship = User__UserGroup(
                user_id=new_user.id,
                user_group_id=-1,
                role=UserRole.ADMIN,
                is_admin=True,
                is_curator=False
            )
            db_session.add(admin_relationship)
        elif current_user_role == UserRole.TEAM_ADMIN and team_admin_team:
            # Team admin creating another team admin or basic user - assign to their team
            team_id = team_admin_team
            team_relationship = User__UserGroup(
                user_id=new_user.id,
                user_group_id=team_id,
                role=role, # Use the requested role
                is_admin=False,
                is_curator=False
            )
            db_session.add(team_relationship)
            # Update invite status since user now has team
            new_invite.status = "ready_to_signup"
        # For global admin inviting non-admin, or other cases, status remains "pending_assignment"
        # until they are explicitly assigned to a team or sign up.

        invited_count += 1

    db_session.commit()
    return invited_count




@router.patch("/manage/admin/remove-invited-user")
def remove_invited_user(
    user_email: UserByEmail,
    _: User | None = Depends(current_team_admin_or_admin_user),
    db_session: Session = Depends(get_session),
) -> int:
    user = db_session.query(User).filter_by(email=user_email.user_email).first()
    if not user:
        return 0
    invite = db_session.query(InvitedUser).filter_by(id=user.id).first()
    if invite:
        db_session.delete(invite)
    # Only delete user if not active
    if not user.is_active:
        db_session.delete(user)
    db_session.commit()
    return 1



@router.patch("/manage/admin/deactivate-user")
def deactivate_user(
    user_email: UserByEmail,
    current_user: User | None = Depends(current_team_admin_or_admin_user),
    db_session: Session = Depends(get_session),
) -> None:
    if current_user is None:
        raise HTTPException(
            status_code=400, detail="Auth is disabled, cannot deactivate user"
        )

    if current_user.email == user_email.user_email:
        raise HTTPException(status_code=400, detail="You cannot deactivate yourself")

    user_to_deactivate = get_user_by_email(
        email=user_email.user_email, db_session=db_session
    )

    if not user_to_deactivate:
        raise HTTPException(status_code=404, detail="User not found")

    if user_to_deactivate.is_active is False:
        logger.warning("{} is already deactivated".format(user_to_deactivate.email))

    if user_to_deactivate.is_active is False:
        logger.warning("{} is already deactivated".format(user_to_deactivate.email))

    user_to_deactivate.is_active = False
    # Also remove from invited users table to prevent "ready_to_signup" status
    invited_user = db_session.query(InvitedUser).filter_by(id=user_to_deactivate.id).first()
    if invited_user:
        db_session.delete(invited_user)
    db_session.add(user_to_deactivate)
    db_session.commit()


@router.delete("/manage/admin/delete-user")
async def delete_user(
    user_email: UserByEmail,
    current_user: User = Depends(current_team_admin_or_admin_user),
    current_team_id: int = Depends(get_current_team_id_from_header),
    db_session: Session = Depends(get_session),
) -> None:
    user_to_delete = get_user_by_email(
        email=user_email.user_email, db_session=db_session
    )
    if not user_to_delete:
        raise HTTPException(status_code=404, detail="User not found")

    # Prevent self-deletion
    if current_user.id == user_to_delete.id:
        raise HTTPException(status_code=400, detail="You cannot delete yourself")

    # Check if the user to delete is part of any user team
    is_part_of_team = db_session.query(User__UserGroup).filter(
        User__UserGroup.user_id == user_to_delete.id
    ).first() is not None

    if current_user.role == UserRole.ADMIN:
        # Admin deleting a user
        if is_part_of_team:
            raise HTTPException(
                status_code=400, detail="Cannot delete user, as it is part of the user_team"
            )
        # If not part of a team, proceed with deletion, but still require deactivation first
        user_status = user_to_delete.status
        if user_status != "inactive":
            logger.warning(
                "{} must be deactivated before deleting (current status: {})".format(
                    user_to_delete.email, user_status
                )
            )
            raise HTTPException(
                status_code=400, detail="User must be deactivated before deleting"
            )
        else:
            # If not part of a team and is inactive, proceed with deletion
            try:
                delete_user_from_db(user_to_delete, db_session)
                logger.info(f"Deleted user {user_to_delete.email} as they are not part of any team and are inactive.")
            except Exception as e:
                db_session.rollback()
                logger.error(f"Error deleting user {user_to_delete.email}: {str(e)}")
                raise HTTPException(status_code=500, detail="Error deleting user")

    elif current_user.role == UserRole.TEAM_ADMIN:
        # Team admin deleting a user
        if not current_team_id:
            raise HTTPException(
                status_code=400, detail="Team admin must have a current team assigned."
            )

        # Check if the user to delete is part of the team admin's current team
        is_user_in_team_admin_team = db_session.query(User__UserGroup).filter(
            User__UserGroup.user_id == user_to_delete.id,
            User__UserGroup.user_group_id == current_team_id
        ).first() is not None

        if not is_user_in_team_admin_team:
            raise HTTPException(
                status_code=400, detail="Cannot delete user, as they are not part of your current team."
            )

        # Remove user from the specific team the team admin is managing
        db_session.query(User__UserGroup).filter(
            User__UserGroup.user_id == user_to_delete.id,
            User__UserGroup.user_group_id == current_team_id
        ).delete()
        db_session.flush() # Flush to ensure changes are applied before checking other teams

        # Check if the user is still part of any other team
        is_still_part_of_any_team = db_session.query(User__UserGroup).filter(
            User__UserGroup.user_id == user_to_delete.id
        ).first() is not None

        if not is_still_part_of_any_team and not user_to_delete.is_active:
            # If user is not part of any team and is inactive, then delete the user entirely
            try:
                delete_user_from_db(user_to_delete, db_session)
                logger.info(f"Deleted user {user_to_delete.email} as they are no longer part of any team and are inactive.")
            except Exception as e:
                db_session.rollback()
                logger.error(f"Error deleting user {user_to_delete.email}: {str(e)}")
                raise HTTPException(status_code=500, detail="Error deleting user")
        else:
            logger.info(f"User {user_to_delete.email} removed from team {current_team_id}. User remains in the system as they are either active or part of other teams.")

    db_session.commit() # Commit changes for both ADMIN and TEAM_ADMIN roles


@router.patch("/manage/admin/activate-user")
def activate_user(
    user_email: UserByEmail,
    _: User | None = Depends(current_team_admin_or_admin_user),
    db_session: Session = Depends(get_session),
) -> None:
    user_to_activate = get_user_by_email(
        email=user_email.user_email, db_session=db_session
    )
    if not user_to_activate:
        raise HTTPException(status_code=404, detail="User not found")

    if user_to_activate.is_active is True:
        logger.warning("{} is already activated".format(user_to_activate.email))

    user_to_activate.is_active = True
    db_session.add(user_to_activate)
    db_session.commit()


@router.get("/manage/admin/valid-domains")
def get_valid_domains(
    _: User | None = Depends(current_team_admin_or_admin_user),
) -> list[str]:
    return VALID_EMAIL_DOMAINS


"""Endpoints for all"""


@router.get("/users")
def list_all_users_basic_info(
    _: User | None = Depends(current_user),
    db_session: Session = Depends(get_session),
) -> list[MinimalUserSnapshot]:
    users = get_all_users(db_session)
    return [MinimalUserSnapshot(id=user.id, email=user.email) for user in users]


@router.get("/user/teams")
def get_user_teams(
    user: User = Depends(current_user),
    db_session: Session = Depends(get_session),
) -> list[UserTeamSnapshot]:
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    user_teams = db_session.query(UserGroup).join(
        User__UserGroup, UserGroup.id == User__UserGroup.user_group_id
    ).filter(
        User__UserGroup.user_id == user.id
    ).all()

    return [UserTeamSnapshot(id=team.id, name=team.name) for team in user_teams]


@router.get("/get-user-role")
async def get_user_role(user: User = Depends(current_user)) -> UserRoleResponse:
    if user is None:
        raise ValueError("Invalid or missing user.")
    return UserRoleResponse(role=user.role)

@router.get("/usersid/{email}")
def get_user_id_by_email(
    email: str,
    _: User = Depends(current_user),
    db_session: Session = Depends(get_session),
) -> UserIdSnapshot:
    email = email.lower()
    for user in get_all_users(db_session):
        if user.email.lower() == email:
            return UserIdSnapshot(id=user.id)
    raise HTTPException(status_code=404, detail="User not found")

def get_current_token_expiration_jwt(
    user: User | None, request: Request
) -> datetime | None:
    if user is None:
        return None

    try:
        # Get the JWT from the cookie
        jwt_token = request.cookies.get(FASTAPI_USERS_AUTH_COOKIE_NAME)
        if not jwt_token:
            logger.error("No JWT token found in cookies")
            return None

        # Decode the JWT
        decoded_token = jwt.decode(jwt_token, options={"verify_signature": False})

        # Get the 'exp' (expiration) claim from the token
        exp = decoded_token.get("exp")
        if exp:
            return datetime.fromtimestamp(exp)
        else:
            logger.error("No 'exp' claim found in JWT")
            return None

    except Exception as e:
        logger.error(f"Error decoding JWT: {e}")
        return None


def get_current_token_creation(
    user: User | None, db_session: Session
) -> datetime | None:
    if user is None:
        return None
    try:
        result = db_session.execute(
            select(AccessToken)
            .where(AccessToken.user_id == user.id)  # type: ignore
            .order_by(desc(Column("created_at")))
            .limit(1)
        )
        access_token = result.scalar_one_or_none()

        if access_token:
            return access_token.created_at
        else:
            logger.error("No AccessToken found for user")
            return None

    except Exception as e:
        logger.error(f"Error fetching AccessToken: {e}")
        return None


@router.get("/me")
def verify_user_logged_in(
    user: User | None = Depends(current_user), # Changed from optional_user to current_user
    db_session: Session = Depends(get_session),
) -> UserInfo:
    tenant_id = get_current_tenant_id()

    # NOTE: this does not use `current_user` / `current_admin_user` because we don't want
    # to enforce user verification here - the frontend always wants to get the info about
    # the current user regardless of if they are currently verified
    if user is None:
        # if auth type is disabled, return a dummy user with preferences from
        # the key-value store
        if AUTH_TYPE == AuthType.DISABLED:
            store = get_kv_store()
            return fetch_no_auth_user(store)
        if anonymous_user_enabled(tenant_id=tenant_id):
            store = get_kv_store()
            return fetch_no_auth_user(store, anonymous_user_enabled=True)

        raise BasicAuthenticationError(detail="User Not Authenticated")
    if user.oidc_expiry and user.oidc_expiry < datetime.now(timezone.utc):
        raise BasicAuthenticationError(
            detail="Access denied. User's OIDC token has expired.",
        )

    token_created_at = (
        None if MULTI_TENANT else get_current_token_creation(user, db_session)
    )
    organization_name = fetch_ee_implementation_or_noop(
        "onyx.server.tenants.user_mapping", "get_tenant_id_for_email", None
    )(user.email)

    user_info = UserInfo.from_model(
        user,
        current_token_created_at=token_created_at,
        expiry_length=SESSION_EXPIRE_TIME_SECONDS,
        is_cloud_superuser=user.email in SUPER_USERS,
        organization_name=organization_name,
    )

    return user_info


"""APIs to adjust user preferences"""


@router.patch("/temperature-override-enabled")
def update_user_temperature_override_enabled(
    temperature_override_enabled: bool,
    user: User | None = Depends(current_user),
    db_session: Session = Depends(get_session),
) -> None:
    if user is None:
        if AUTH_TYPE == AuthType.DISABLED:
            store = get_kv_store()
            no_auth_user = fetch_no_auth_user(store)
            no_auth_user.preferences.temperature_override_enabled = (
                temperature_override_enabled
            )
            set_no_auth_user_preferences(store, no_auth_user.preferences)
            return
        else:
            raise RuntimeError("This should never happen")

    db_session.execute(
        update(User)
        .where(User.id == user.id)  # type: ignore
        .values(temperature_override_enabled=temperature_override_enabled)
    )
    db_session.commit()


class ChosenDefaultModelRequest(BaseModel):
    default_model: str | None = None


@router.patch("/shortcut-enabled")
def update_user_shortcut_enabled(
    shortcut_enabled: bool,
    user: User | None = Depends(current_user),
    db_session: Session = Depends(get_session),
) -> None:
    if user is None:
        if AUTH_TYPE == AuthType.DISABLED:
            store = get_kv_store()
            no_auth_user = fetch_no_auth_user(store)
            no_auth_user.preferences.shortcut_enabled = shortcut_enabled
            set_no_auth_user_preferences(store, no_auth_user.preferences)
            return
        else:
            raise RuntimeError("This should never happen")

    db_session.execute(
        update(User)
        .where(User.id == user.id)  # type: ignore
        .values(shortcut_enabled=shortcut_enabled)
    )
    db_session.commit()


@router.patch("/auto-scroll")
def update_user_auto_scroll(
    request: AutoScrollRequest,
    user: User | None = Depends(current_user),
    db_session: Session = Depends(get_session),
) -> None:
    if user is None:
        if AUTH_TYPE == AuthType.DISABLED:
            store = get_kv_store()
            no_auth_user = fetch_no_auth_user(store)
            no_auth_user.preferences.auto_scroll = request.auto_scroll
            set_no_auth_user_preferences(store, no_auth_user.preferences)
            return
        else:
            raise RuntimeError("This should never happen")

    db_session.execute(
        update(User)
        .where(User.id == user.id)  # type: ignore
        .values(auto_scroll=request.auto_scroll)
    )
    db_session.commit()


@router.patch("/user/default-model")
def update_user_default_model(
    request: ChosenDefaultModelRequest,
    user: User | None = Depends(current_user),
    db_session: Session = Depends(get_session),
) -> None:
    if user is None:
        if AUTH_TYPE == AuthType.DISABLED:
            store = get_kv_store()
            no_auth_user = fetch_no_auth_user(store)
            no_auth_user.preferences.default_model = request.default_model
            set_no_auth_user_preferences(store, no_auth_user.preferences)
            return
        else:
            raise RuntimeError("This should never happen")

    db_session.execute(
        update(User)
        .where(User.id == user.id)  # type: ignore
        .values(default_model=request.default_model)
    )
    db_session.commit()


def _handle_role_transition(
    db_session: Session,
    user_to_update: User,
    current_role: UserRole,
    requested_role: UserRole,
    current_team_id: int | None = None
) -> None:
    """
    Handles role transitions
    """
    from onyx.db.models import User__UserGroup

    # Ensure current_team_id is provided for team-scoped operations
    if current_team_id is None:
        raise HTTPException(
            status_code=400,
            detail="current_team_id must be provided for team-scoped role transitions."
        )

    # Determine the filter for User__UserGroup based on current_team_id
    user_group_filter = [
        User__UserGroup.user_id == user_to_update.id,
        User__UserGroup.user_group_id == current_team_id
    ]

    # Case: Team Admin -> Basic (demote within team)
    if current_role == UserRole.TEAM_ADMIN and requested_role == UserRole.BASIC:
        # Update existing team relationships to Basic role, restricted by current_team_id
        db_session.query(User__UserGroup).filter(
            *user_group_filter
        ).update({
            User__UserGroup.role: UserRole.BASIC,
            User__UserGroup.is_admin: False
        })
        user_to_update.is_admin = False

    # Case: Basic -> Team Admin (promote within team)
    elif current_role == UserRole.BASIC and requested_role == UserRole.TEAM_ADMIN:
        # Update existing team relationships to Team Admin role, restricted by current_team_id
        db_session.query(User__UserGroup).filter(
            *user_group_filter
        ).update({
            User__UserGroup.role: UserRole.TEAM_ADMIN,
            User__UserGroup.is_admin: False
        })
        user_to_update.is_admin = False
    # No else block needed, as set_user_role handles invalid transitions



class ReorderPinnedAssistantsRequest(BaseModel):
    ordered_assistant_ids: list[int]


@router.patch("/user/pinned-assistants")
def update_user_pinned_assistants(
    request: ReorderPinnedAssistantsRequest,
    user: User = Depends(current_user), # Ensure user is not None
    current_team_id: int = Depends(get_current_team_id_from_header),
    db_session: Session = Depends(get_session),
) -> None:
    ordered_assistant_ids = request.ordered_assistant_ids

    if user is None:
        if AUTH_TYPE == AuthType.DISABLED:
            store = get_kv_store()
            no_auth_user = fetch_no_auth_user(store)
            no_auth_user.preferences.pinned_assistants = ordered_assistant_ids
            set_no_auth_user_preferences(store, no_auth_user.preferences)
            return
        else:
            raise HTTPException(status_code=400, detail="User not authenticated")

    if current_team_id is None or current_team_id == 0:
        raise HTTPException(status_code=400, detail="Invalid or missing team ID for pinning assistants.")

    # Find the specific User__UserGroup entry for the current user and team
    user_group_relationship = db_session.query(User__UserGroup).filter(
        User__UserGroup.user_id == user.id,
        User__UserGroup.user_group_id == current_team_id
    ).first()

    if not user_group_relationship:
        # This could happen if the user's current_team_id is set to a team they are not actually part of
        raise HTTPException(status_code=404, detail=f"User is not associated with team ID {current_team_id}. Cannot pin assistants.")

    # Directly update the pinned_assistants on the User__UserGroup entry
    user_group_relationship.pinned_assistants = ordered_assistant_ids
    db_session.commit()


class ChosenAssistantsRequest(BaseModel):
    chosen_assistants: list[int]


def update_assistant_visibility(
    preferences: UserPreferences, assistant_id: int, show: bool
) -> UserPreferences:
    visible_assistants = preferences.visible_assistants or []
    hidden_assistants = preferences.hidden_assistants or []

    if show:
        if assistant_id not in visible_assistants:
            visible_assistants.append(assistant_id)
        if assistant_id in hidden_assistants:
            hidden_assistants.remove(assistant_id)
    else:
        if assistant_id in visible_assistants:
            visible_assistants.remove(assistant_id)
        if assistant_id not in hidden_assistants:
            hidden_assistants.append(assistant_id)

    preferences.visible_assistants = visible_assistants
    preferences.hidden_assistants = hidden_assistants
    return preferences


@router.patch("/user/assistant-list/update/{assistant_id}")
def update_user_assistant_visibility(
    assistant_id: int,
    show: bool,
    user: User | None = Depends(current_user),
    db_session: Session = Depends(get_session),
) -> None:
    if user is None:
        if AUTH_TYPE == AuthType.DISABLED:
            store = get_kv_store()
            no_auth_user = fetch_no_auth_user(store)
            preferences = no_auth_user.preferences
            updated_preferences = update_assistant_visibility(
                preferences, assistant_id, show
            )
            if updated_preferences.chosen_assistants is not None:
                updated_preferences.chosen_assistants.append(assistant_id)

            set_no_auth_user_preferences(store, updated_preferences)
            return
        else:
            raise RuntimeError("This should never happen")

    user_preferences = UserInfo.from_model(user).preferences
    updated_preferences = update_assistant_visibility(
        user_preferences, assistant_id, show
    )
    if updated_preferences.chosen_assistants is not None:
        updated_preferences.chosen_assistants.append(assistant_id)
    db_session.execute(
        update(User)
        .where(User.id == user.id)  # type: ignore
        .values(
            hidden_assistants=updated_preferences.hidden_assistants,
            visible_assistants=updated_preferences.visible_assistants,
            chosen_assistants=updated_preferences.chosen_assistants,
        )
    )
    db_session.commit()
