from fastapi import APIRouter
from fastapi import Depends
from fastapi import HTT<PERSON>Exception
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session
from pydantic import BaseModel

from onyx.db.user_teams import get_user_teams

from onyx.db.user_teams import create_user_teams
from onyx.db.user_teams import prepare_user_teams_for_deletion
from onyx.db.user_teams import update_user_teams
from onyx.server.user_teams.models import UserTeams
from onyx.server.user_teams.models import CreateUserTeams
from onyx.server.user_teams.models import UpdateUserTeams
from onyx.server.user_teams.models import UserTeamMember
from onyx.auth.users import current_admin_user
from onyx.auth.users import current_curator_or_admin_user
from onyx.auth.users import current_team_admin_or_admin_user
from onyx.auth.users import get_current_team_id_from_header
from onyx.db.engine import get_session
from onyx.db.models import User
from onyx.db.models import User<PERSON><PERSON>
from onyx.db.models import UserGroup

from onyx.db.models import User__UserGroup # Added this import

router = APIRouter(prefix="/manage")


@router.get("/admin/user-teams")
def list_user_teams(
    user: User | None = Depends(current_team_admin_or_admin_user),
    current_team_id: int = Depends(get_current_team_id_from_header),
    db_session: Session = Depends(get_session),
    only_up_to_date: bool = False
) -> list[UserTeams]:
    current_user_role = user.role if user else None
    if user is None or current_user_role == UserRole.ADMIN:
        # Admin users can see all user teams
        user_teams = get_user_teams(db_session, only_up_to_date=only_up_to_date)
    else:
        # Team admin users can only see their current team
        if not current_team_id:
            return []
        user_teams = [get_user_teams(db_session, user_team_ids=[current_team_id], only_up_to_date=only_up_to_date)[0]]

    return [UserTeams.from_model(user_team) for user_team in user_teams]

@router.post("/admin/user-teams")
def create_user_team(
    user_team: CreateUserTeams,
    _: User | None = Depends(current_team_admin_or_admin_user),
    db_session: Session = Depends(get_session),
) -> UserTeams:
    try:
        db_user_team = create_user_teams(db_session, user_team)
    except IntegrityError:
        raise HTTPException(
            400,
            f"User team with name '{user_team.name}' already exists. Please "
            + "choose a different name.",
        )
    return UserTeams.from_model(db_user_team)


@router.patch("/admin/user-teams/{user_team_id}")
def patch_user_teams(
    user_team_id: int,
    user_team_update: UpdateUserTeams,
    user: User | None = Depends(current_team_admin_or_admin_user),
    db_session: Session = Depends(get_session),
) -> UserTeams:
    try:
        return UserTeams.from_model(
            update_user_teams(
                db_session=db_session,
                user=user,
                user_team_id=user_team_id,
                user_team_update=user_team_update,
            )
        )
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))


class UpdateUserTeamRoleRequest(BaseModel):
    user_email: str
    new_role: UserRole


@router.patch("/admin/user-teams/{user_team_id}/user-role")
def update_user_role_in_team(
    user_team_id: int,
    role_update: UpdateUserTeamRoleRequest,
    current_user: User | None = Depends(current_team_admin_or_admin_user),
    current_team_id: int = Depends(get_current_team_id_from_header),
    db_session: Session = Depends(get_session),
) -> dict:
    """
    Update a user's role within a specific team.

    - Admin users can update any user's role in any team
    - Team admin users can only update roles for users in their own teams
    """
    from onyx.db.users import get_user_by_email
    from onyx.db.models import User__UserGroup, UserGroup

    # Get the target user
    target_user = get_user_by_email(email=role_update.user_email, db_session=db_session)
    if not target_user:
        raise HTTPException(status_code=404, detail="User not found")

    # Get the team
    team = db_session.query(UserGroup).filter(UserGroup.id == user_team_id).first()
    if not team:
        raise HTTPException(status_code=404, detail="Team not found")

    # Check if user is a member of this team
    user_team_rel = db_session.query(User__UserGroup).filter(
        User__UserGroup.user_id == target_user.id,
        User__UserGroup.user_group_id == user_team_id
    ).first()

    if not user_team_rel:
        raise HTTPException(
            status_code=400,
            detail="User is not a member of this team"
        )

    # Permission checks
    current_user_role = current_user.role
    if current_user_role == UserRole.TEAM_ADMIN:
        # Team admin can only modify users in their current team
        if current_team_id != user_team_id:
            raise HTTPException(
                status_code=403,
                detail="Team admin can only modify roles in their current team"
            )

    # Update the role
    user_team_rel.role = role_update.new_role
    db_session.commit()

    return {
        "message": f"Updated {role_update.user_email}'s role to {role_update.new_role.value} in team {team.name}"
    }


@router.delete("/admin/user-teams/users/{user_id}")
def remove_user_from_team(
    user_id: str,
    current_user: User = Depends(current_team_admin_or_admin_user),
    current_team_id: int = Depends(get_current_team_id_from_header),
    db_session: Session = Depends(get_session),
) -> None:
    # Verify the team exists and the current user has permissions to modify it
    team = db_session.query(UserGroup).filter_by(id=current_team_id).first()
    if not team:
        raise HTTPException(status_code=404, detail="Team not found")

    # Verify the user exists
    user_to_remove = db_session.query(User).filter_by(id=user_id).first()
    if not user_to_remove:
        raise HTTPException(status_code=404, detail="User not found")

    # Prevent user from removing themselves from a team if they are the only admin of that team
    # This logic might need to be more sophisticated depending on business rules
    # For now, a simple check:
    if current_user.id == user_to_remove.id and current_user.role == UserRole.TEAM_ADMIN:
        # Check if this is the only team admin in this team
        team_admins_in_team = db_session.query(User__UserGroup).filter(
            User__UserGroup.user_group_id == current_team_id,
            User__UserGroup.role == UserRole.TEAM_ADMIN
        ).count()
        if team_admins_in_team == 1:
            raise HTTPException(status_code=400, detail="Cannot remove yourself as the sole team admin from this team.")

    # Check if the current user has permission to remove the target user from this team
    if current_user.role == UserRole.TEAM_ADMIN:
        # Team admin can only remove users from teams they are a member of
        if current_team_id not in current_user.user_team_ids:
            raise HTTPException(status_code=403, detail="Team admin can only manage users in their assigned team.")

        # Team admin cannot remove global admins
        if user_to_remove.is_admin:
            raise HTTPException(status_code=403, detail="Team admin cannot remove a global admin.")

    # Remove the user's association with the specified team
    user_team_association = db_session.query(User__UserGroup).filter(
        User__UserGroup.user_id == user_id,
        User__UserGroup.user_group_id == current_team_id
    ).first()

    if not user_team_association:
        raise HTTPException(status_code=404, detail="User is not part of this team.")

    db_session.delete(user_team_association)
    db_session.commit()


@router.delete("/admin/user-teams/{user_team_id}")
def delete_user_teams(
    user_team_id: int,
    _: User | None = Depends(current_team_admin_or_admin_user),
    db_session: Session = Depends(get_session),
) -> None:
    try:
        prepare_user_teams_for_deletion(db_session, user_team_id)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
