#!/usr/bin/env python3
"""
Simple test script to verify team validation logic for connector-credential pairs.
This script tests the new validation that ensures credentials and connectors 
share at least one common team.
"""

import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

def test_team_validation_logic():
    """Test the core team validation logic"""
    
    # Test case 1: Teams overlap - should pass
    credential_teams = [1, 2, 3]
    connector_teams = [3, 4, 5]
    
    has_overlap = bool(set(credential_teams).intersection(set(connector_teams)))
    print(f"Test 1 - Teams overlap: credential_teams={credential_teams}, connector_teams={connector_teams}")
    print(f"Result: {'PASS' if has_overlap else 'FAIL'} - Has overlap: {has_overlap}")
    assert has_overlap, "Should have overlap on team 3"
    
    # Test case 2: No teams overlap - should fail
    credential_teams = [1, 2, 3]
    connector_teams = [4, 5, 6]
    
    has_overlap = bool(set(credential_teams).intersection(set(connector_teams)))
    print(f"\nTest 2 - No teams overlap: credential_teams={credential_teams}, connector_teams={connector_teams}")
    print(f"Result: {'FAIL' if not has_overlap else 'PASS'} - Has overlap: {has_overlap}")
    assert not has_overlap, "Should not have overlap"
    
    # Test case 3: Empty credential teams - should pass (public credential)
    credential_teams = []
    connector_teams = [1, 2, 3]
    
    # According to our logic, if credential has no teams, validation is skipped
    should_validate = bool(credential_teams)
    print(f"\nTest 3 - Empty credential teams: credential_teams={credential_teams}, connector_teams={connector_teams}")
    print(f"Result: {'PASS' if not should_validate else 'FAIL'} - Should validate: {should_validate}")
    assert not should_validate, "Should skip validation for public credentials"
    
    # Test case 4: Empty connector teams - should pass (public connector)
    credential_teams = [1, 2, 3]
    connector_teams = []
    
    # According to our logic, if connector has no teams (public), validation is skipped
    should_validate = bool(connector_teams)
    print(f"\nTest 4 - Empty connector teams: credential_teams={credential_teams}, connector_teams={connector_teams}")
    print(f"Result: {'PASS' if not should_validate else 'FAIL'} - Should validate: {should_validate}")
    assert not should_validate, "Should skip validation for public connectors"
    
    print("\n✅ All team validation logic tests passed!")

def simulate_validation_function(credential_teams, connector_teams, access_type="private"):
    """Simulate the validation logic from our implementation"""
    
    # Skip validation for public access types
    if access_type == "public" or not connector_teams:
        return True, "Validation skipped for public access"
    
    # If credential has team associations, ensure there's overlap with connector teams
    if credential_teams:
        if not set(credential_teams).intersection(set(connector_teams)):
            return False, "Credential and Connector do not share any common teams"
    
    return True, "Validation passed"

def test_validation_function():
    """Test the simulated validation function"""
    
    print("\n" + "="*60)
    print("Testing simulated validation function")
    print("="*60)
    
    test_cases = [
        # (credential_teams, connector_teams, access_type, expected_result, description)
        ([1, 2], [2, 3], "private", True, "Teams overlap"),
        ([1, 2], [3, 4], "private", False, "No teams overlap"),
        ([1, 2], [2, 3], "public", True, "Public access - validation skipped"),
        ([], [1, 2], "private", True, "Empty credential teams - validation skipped"),
        ([1, 2], [], "private", True, "Empty connector teams - validation skipped"),
        ([1, 2, 3], [1], "private", True, "Single team overlap"),
        ([5], [1, 2, 3, 4, 5], "private", True, "Credential team in connector teams"),
    ]
    
    for i, (cred_teams, conn_teams, access_type, expected, description) in enumerate(test_cases, 1):
        result, message = simulate_validation_function(cred_teams, conn_teams, access_type)
        status = "✅ PASS" if result == expected else "❌ FAIL"
        print(f"Test {i}: {description}")
        print(f"  Credential teams: {cred_teams}")
        print(f"  Connector teams: {conn_teams}")
        print(f"  Access type: {access_type}")
        print(f"  Expected: {'Pass' if expected else 'Fail'}, Got: {'Pass' if result else 'Fail'}")
        print(f"  Message: {message}")
        print(f"  Status: {status}")
        print()
        
        assert result == expected, f"Test {i} failed: {description}"
    
    print("✅ All validation function tests passed!")

if __name__ == "__main__":
    print("Testing team validation logic for connector-credential pairs")
    print("="*60)
    
    try:
        test_team_validation_logic()
        test_validation_function()
        print("\n🎉 All tests passed! Team validation logic is working correctly.")
    except AssertionError as e:
        print(f"\n❌ Test failed: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)
